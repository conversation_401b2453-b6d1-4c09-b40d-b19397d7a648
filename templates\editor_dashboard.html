{% extends "base.html" %}

{% block title %}Editor Dashboard - MediaFlow Pro{% endblock %}

{% block content %}
<style>
/* Progress Steps Styling */
.progress-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 2rem;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 100%;
    width: 4rem;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step.active:not(:last-child)::after {
    background-color: #28a745;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.step.active .step-number {
    background-color: #28a745;
    color: white;
}

.step.completed .step-number {
    background-color: #007bff;
    color: white;
}

.step-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    text-align: center;
}

.step.active .step-label {
    color: #28a745;
    font-weight: bold;
}

.step.completed .step-label {
    color: #007bff;
    font-weight: bold;
}

/* Enhanced Modal Styling */
.modal-xl .modal-content {
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.form-control-lg {
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* Animation for form validation */
.is-valid {
    animation: validPulse 0.5s ease-in-out;
}

@keyframes validPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Loading state */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-edit me-2 text-warning"></i>Editor Dashboard
                    </h1>
                    <p class="text-muted mb-0">Content Editing & Final Review</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-warning fs-6 px-3 py-2">{{ session.role }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Editing
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ editing_items|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Completed Today
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_completed }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Completed
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ completed_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-award fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Editing Queue -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-list-check me-2"></i>Editing Queue
                    </h6>
                </div>
                <div class="card-body">
                    {% if editing_items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Folder Name</th>
                                    <th>Executor</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in editing_items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-folder text-warning me-2"></i>
                                            <div>
                                                <strong>{{ item[6] }}</strong>
                                                {% if item[7] and item[7].soft_renamed %}
                                                <br>
                                                <small class="text-info">
                                                    <i class="fas fa-tag me-1"></i>
                                                    <strong>Soft Renamed:</strong> {{ item[7].soft_renamed }}
                                                </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <small class="text-muted">{{ item[8] or 'Path not set' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ item[9] }}</span>
                                    </td>
                                    <td>
                                        {% if item[2] == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif item[2] == 'in_progress' %}
                                            <span class="badge bg-info">In Progress</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ item[3] }}</small>
                                    </td>
                                    <td>
                                        {% if item[2] == 'pending' %}
                                            <button class="btn btn-sm btn-success me-2"
                                                    onclick="startEditing({{ item[0] }}, '{{ item[6] }}')">
                                                <i class="fas fa-play me-1"></i>Start Editing
                                            </button>
                                        {% elif item[2] == 'in_progress' %}
                                            <button class="btn btn-sm btn-primary me-2"
                                                    onclick="viewEditingDetails({{ item[0] }}, '{{ item[6] }}', '{{ item[7] }}')">
                                                <i class="fas fa-eye me-1"></i>View Details
                                            </button>
                                            <button class="btn btn-sm btn-success"
                                                    onclick="completeEditing({{ item[0] }}, '{{ item[6] }}')">
                                                <i class="fas fa-check me-1"></i>Mark as Done
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No items in editing queue</h5>
                        <p class="text-muted">Items will appear here when flagged for editing by Cross-Checkers.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Editing Details Modal -->
<div class="modal fade" id="editingDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Editing Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="editingDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Complete Editing Modal -->
<div class="modal fade" id="completeEditingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>Complete Editing - Make as Done
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="completeEditingForm">
                    <!-- Progress Steps -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="progress-steps">
                                <div class="step active">
                                    <div class="step-number">1</div>
                                    <div class="step-label">Source Folder</div>
                                </div>
                                <div class="step">
                                    <div class="step-number">2</div>
                                    <div class="step-label">Destination</div>
                                </div>
                                <div class="step">
                                    <div class="step-number">3</div>
                                    <div class="step-label">Review & Submit</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Source Folder Selection -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-folder-open me-2"></i>Step 1: Select Source Folder
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Choose the folder that contains the completed editing work to be moved.</p>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-lg" id="sourceFolder"
                                               placeholder="Click Browse to select the source folder..."
                                               readonly>
                                        <button type="button" class="btn btn-primary btn-lg" onclick="browseSourceFolder()">
                                            <i class="fas fa-search me-2"></i>Browse Source Folder
                                        </button>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Select the folder containing the completed editing work that needs to be moved.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Destination Folder Selection -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-folder me-2"></i>Step 2: Select Destination Folder
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Choose where the completed work should be moved to.</p>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-lg" id="destinationFolder"
                                               placeholder="Click Browse to select the destination folder..."
                                               readonly>
                                        <button type="button" class="btn btn-success btn-lg" onclick="browseDestinationFolder()">
                                            <i class="fas fa-search me-2"></i>Browse Destination Folder
                                        </button>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Select the final destination where the completed work will be stored.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Editing Notes and Admin Approval -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-sticky-note me-2"></i>Editing Notes
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <textarea class="form-control" id="editingNotes" rows="6"
                                              placeholder="Add detailed notes about the editing work completed, changes made, quality checks performed, etc..."></textarea>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Provide comprehensive notes about the editing work for review.
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-shield me-2"></i>Admin Review Request
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="requestAdminApproval" checked>
                                        <label class="form-check-label fw-bold" for="requestAdminApproval">
                                            Request Admin Approval for Review
                                        </label>
                                    </div>
                                    <div class="alert alert-info mb-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Recommended:</strong> Admin review ensures quality control and proper workflow completion.
                                    </div>
                                    <textarea class="form-control" id="adminReviewNotes" rows="3"
                                              placeholder="Add specific notes for admin review (optional)..."></textarea>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Specify any particular aspects that need admin attention.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Section -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-clipboard-check me-2"></i>Summary
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-2"><strong>Source:</strong> <span id="sourceSummary" class="text-muted">Not selected</span></p>
                                            <p class="mb-2"><strong>Destination:</strong> <span id="destinationSummary" class="text-muted">Not selected</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-2"><strong>Admin Review:</strong> <span id="adminReviewSummary" class="text-success">Requested</span></p>
                                            <p class="mb-2"><strong>Status:</strong> <span id="readyStatus" class="text-warning">Waiting for folder selection</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-success btn-lg" id="confirmCompleteBtn" disabled>
                    <i class="fas fa-check-circle me-2"></i>
                    <span id="submitButtonText">Complete Editing & Move Folder</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='editor.js') }}"></script>
{% endblock %}
