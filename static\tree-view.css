/* Tree View Styles for Executor Folder Browser */

.tree-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    max-height: 500px;
    overflow-y: auto;
}

.tree-node {
    margin: 0;
    padding: 0;
}

.tree-item {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.5rem;
    margin: 0.125rem 0;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    position: relative;
}

.tree-item:hover,
.tree-item.hover {
    background-color: #e9ecef;
    transform: translateX(2px);
}

.tree-item.selected {
    background-color: #0d6efd;
    color: white;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

.tree-item.selected .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

.tree-item.selected .text-warning {
    color: #ffc107 !important;
}

.tree-item.selected .text-info {
    color: #0dcaf0 !important;
}

.tree-item.selected .text-success {
    color: #198754 !important;
}

.tree-item.selected .text-danger {
    color: #dc3545 !important;
}

.tree-toggle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-right: 0.25rem;
    cursor: pointer;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.tree-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.tree-toggle i {
    font-size: 0.75rem;
    transition: transform 0.2s ease;
}

.tree-toggle.expanded i {
    transform: rotate(0deg);
}

.tree-toggle.collapsed i {
    transform: rotate(0deg);
}

.tree-spacer {
    width: 20px;
    height: 20px;
    margin-right: 0.25rem;
    flex-shrink: 0;
}

.tree-icon {
    margin-right: 0.5rem;
    flex-shrink: 0;
    display: flex;
    align-items: center;
}

.tree-icon i {
    font-size: 1rem;
}

.tree-label {
    flex-grow: 1;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tree-meta {
    margin-left: auto;
    flex-shrink: 0;
}

.tree-badge {
    margin-left: auto;
    flex-shrink: 0;
}

.tree-children {
    margin-left: 1rem;
    border-left: 1px dashed #dee2e6;
    padding-left: 0.5rem;
}

/* Simple show/hide - no complex animations */
.tree-children[style*="display: none"] {
    display: none !important;
}

.tree-children[style*="display: block"] {
    display: block !important;
}

.tree-root {
    margin-bottom: 1rem;
}

.tree-root > .tree-item {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tree-root > .tree-item.selected {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.tree-file .tree-item {
    padding-left: 1.5rem;
}

.tree-folder .tree-item {
    font-weight: 500;
}

/* Animation for expand/collapse */
@keyframes treeExpand {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 1000px;
        opacity: 1;
    }
}

@keyframes treeCollapse {
    from {
        max-height: 1000px;
        opacity: 1;
    }
    to {
        max-height: 0;
        opacity: 0;
    }
}

/* Folder statistics cards */
.folder-stats {
    margin-bottom: 1.5rem;
}

.folder-stats .card {
    transition: transform 0.2s ease;
}

.folder-stats .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Selection info */
.selection-info {
    margin-top: 1rem;
}

.selection-info .alert {
    margin-bottom: 0;
    border-left: 4px solid #0d6efd;
}

/* Scrollbar styling */
.tree-container::-webkit-scrollbar {
    width: 8px;
}

.tree-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.tree-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.tree-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tree-container {
        max-height: 400px;
    }
    
    .tree-item {
        padding: 0.5rem 0.25rem;
    }
    
    .tree-label {
        font-size: 0.9rem;
    }
    
    .tree-meta {
        display: none;
    }
    
    .tree-badge .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Loading state */
.tree-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6c757d;
}

.tree-loading i {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty state */
.tree-empty {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.tree-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* File type specific styling */
.tree-item .fa-file-video {
    color: #dc3545 !important;
}

.tree-item .fa-file-audio {
    color: #198754 !important;
}

.tree-item .fa-file-image {
    color: #0dcaf0 !important;
}

.tree-item .fa-file-pdf {
    color: #dc3545 !important;
}

.tree-item .fa-file-word {
    color: #0d6efd !important;
}

.tree-item .fa-file-archive {
    color: #ffc107 !important;
}

.tree-item .fa-file-code {
    color: #6f42c1 !important;
}

/* Hover effects for different file types */
.tree-item:hover .fa-file-video,
.tree-item.selected .fa-file-video {
    filter: brightness(1.2);
}

.tree-item:hover .fa-file-audio,
.tree-item.selected .fa-file-audio {
    filter: brightness(1.2);
}

.tree-item:hover .fa-file-image,
.tree-item.selected .fa-file-image {
    filter: brightness(1.2);
}
