#!/usr/bin/env python3
"""
Archives MediaFlow Pro - Professional Content Management System
A comprehensive Flask application for archives media content processing and workflow management
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory
from flask_cors import CORS
import sqlite3
import os
import json
import gspread
from google.oauth2.service_account import Credentials
from datetime import datetime, timedelta
import pytz
import subprocess
import tempfile
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import subprocess
import shutil
from pathlib import Path
import logging
from logging.handlers import RotatingFileHandler

# Simple but effective SSL bypass for corporate environments
import ssl
import urllib3
import warnings

# Disable all SSL warnings
try:
    urllib3.disable_warnings()
except:
    pass

try:
    warnings.filterwarnings('ignore', message='Unverified HTTPS request')
except:
    pass

# Set environment variables
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# Monkey patch SSL
ssl._create_default_https_context = ssl._create_unverified_context

print("🔧 SSL bypass enabled for corporate environment")

app = Flask(__name__)
app.secret_key = 'archives-mediaflow-pro-2024-secure-key'

# Configure CORS for file uploads
CORS(app, resources={
    r"/api/*": {
        "origins": "*",
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"]
    }
})

# Configure file upload limits for video duration detection
app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024 * 1024  # 2GB max file size
app.config['UPLOAD_TIMEOUT'] = 300  # 5 minutes timeout

# Configuration
DATABASE_PATH = "archives_mediaflow_pro.db"

# Logging Configuration
def setup_logging():
    """Setup comprehensive logging system"""
    # Create logs directory
    os.makedirs('logs', exist_ok=True)

    # Configure main application logger
    app_logger = logging.getLogger('archives_mediaflow')
    app_logger.setLevel(logging.INFO)

    # Create rotating file handler
    file_handler = RotatingFileHandler(
        'logs/system.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )

    # Create console handler
    console_handler = logging.StreamHandler()

    # Create formatter
    formatter = logging.Formatter(
        '[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    app_logger.addHandler(file_handler)
    app_logger.addHandler(console_handler)

    return app_logger

# Initialize logging
logger = setup_logging()

# Logging Decorators
def log_action(action_type):
    """Decorator to log user actions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = session.get('user_id')
            username = session.get('username')
            role = session.get('role')

            start_time = datetime.now()

            try:
                result = f(*args, **kwargs)

                # Log successful action
                duration = (datetime.now() - start_time).total_seconds()
                logger.info(f"{action_type} | User: {username} ({role}) | Duration: {duration:.2f}s | SUCCESS")

                # Also log to database
                if user_id:
                    try:
                        conn = sqlite3.connect(DATABASE_PATH)
                        cursor = conn.cursor()
                        log_system_action(cursor, user_id, action_type, f"Duration: {duration:.2f}s | SUCCESS")
                        conn.commit()
                        conn.close()
                    except:
                        pass  # Don't fail the main action if logging fails

                return result

            except Exception as e:
                # Log failed action
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(f"{action_type} | User: {username} ({role}) | Duration: {duration:.2f}s | ERROR: {str(e)}")

                # Also log to database
                if user_id:
                    try:
                        conn = sqlite3.connect(DATABASE_PATH)
                        cursor = conn.cursor()
                        log_system_action(cursor, user_id, action_type, f"Duration: {duration:.2f}s | ERROR: {str(e)}")
                        conn.commit()
                        conn.close()
                    except:
                        pass

                raise e

        return decorated_function
    return decorator

GOOGLE_SHEET_ID = "1yw98-IkQB33U6gsZuhKDtiGaolTrEQM-s6Ty5fF6Tj4"  # For search functionality
MAIN_SHEET_ID = "12vG_869ivHGQmPRcXib03ZqQ1KbErGwgKw9TG-txuQ4"  # For data upload
SHEET_NAMES = ["SG", "IF", "IG"]
MAIN_SHEET_NAME = "Main Sheet"

# Initialize Google Sheets with aggressive SSL bypass
def init_google_sheets():
    """Initialize Google Sheets connection with aggressive SSL bypass"""
    try:
        # Patch requests at the module level before importing gspread
        import requests
        import requests.adapters
        import ssl

        # Create SSL bypass adapter
        class SSLBypassAdapter(requests.adapters.HTTPAdapter):
            def init_poolmanager(self, *args, **kwargs):
                kwargs['ssl_context'] = ssl.create_default_context()
                kwargs['ssl_context'].check_hostname = False
                kwargs['ssl_context'].verify_mode = ssl.CERT_NONE
                return super().init_poolmanager(*args, **kwargs)

        # Patch the default session
        original_session_init = requests.Session.__init__
        def patched_session_init(self):
            original_session_init(self)
            self.verify = False
            self.mount('https://', SSLBypassAdapter())
            self.mount('http://', SSLBypassAdapter())

        requests.Session.__init__ = patched_session_init

        # Patch module-level request function
        original_request = requests.request
        def patched_request(*args, **kwargs):
            kwargs['verify'] = False
            return original_request(*args, **kwargs)

        requests.request = patched_request

        print("✅ Requests module patched for SSL bypass")

        # Now import and use gspread
        scope = [
            "https://spreadsheets.google.com/feeds",
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/spreadsheets"
        ]

        creds = Credentials.from_service_account_file("credentials.json", scopes=scope)
        client = gspread.authorize(creds)

        print("✅ Google Sheets connection established with SSL bypass")
        return client

    except Exception as e:
        print(f"❌ Google Sheets initialization error: {e}")
        print(f"Error type: {type(e).__name__}")

        # If it's still an SSL error, show specific message
        if "SSL" in str(e) or "certificate" in str(e).lower():
            print("🔧 SSL certificate issue detected")
            print("This may require network administrator assistance")

        return None

# Database initialization
def init_database():
    """Initialize SQLite database"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
    ''')
    
    # Processing queue table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS processing_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            folder_path TEXT NOT NULL,
            folder_name TEXT NOT NULL,
            executor_id INTEGER,
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP,
            metadata TEXT,
            FOREIGN KEY (executor_id) REFERENCES users (id)
        )
    ''')
    
    # Verification queue table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS verification_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            processing_id INTEGER,
            cross_checker_id INTEGER,
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            verified_at TIMESTAMP,
            notes TEXT,
            editing_required BOOLEAN DEFAULT 0,
            audio_extracted BOOLEAN DEFAULT 0,
            folder_renamed BOOLEAN DEFAULT 0,
            google_sheet_updated BOOLEAN DEFAULT 0,
            audio_file_path TEXT,
            final_folder_path TEXT,
            FOREIGN KEY (processing_id) REFERENCES processing_queue (id),
            FOREIGN KEY (cross_checker_id) REFERENCES users (id)
        )
    ''')

    # Editing queue table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS editing_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            verification_id INTEGER,
            editor_id INTEGER,
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            notes TEXT,
            final_editor_path TEXT,
            FOREIGN KEY (verification_id) REFERENCES verification_queue (id),
            FOREIGN KEY (editor_id) REFERENCES users (id)
        )
    ''')

    # System logs table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            details TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Create default users
    default_users = [
        ('executor1', 'password123', 'Executor 1'),
        ('executor2', 'password123', 'Executor 2'),
        ('executor3', 'password123', 'Executor 3'),
        ('crosschecker', 'password123', 'Cross-Checker'),
        ('editor', 'password123', 'Editor'),
        ('admin', 'password123', 'Admin')
    ]
    
    for username, password, role in default_users:
        cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
        if not cursor.fetchone():
            password_hash = generate_password_hash(password)
            cursor.execute(
                'INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)',
                (username, password_hash, role)
            )
    
    conn.commit()
    conn.close()

def ensure_required_directories():
    """Ensure required directories exist for the application"""
    required_dirs = [
        'E:\\Work space',
        'E:\\To Be Deleted',
        'E:\\To Be Ingested',
        'E:\\For Editing',
        'E:\\Extracted Audios'
    ]

    for directory in required_dirs:
        try:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"DIRECTORY | Ensured directory exists: {directory}")
        except Exception as e:
            logger.error(f"DIRECTORY | Failed to create directory {directory}: {e}")

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            # Check if this is an API request
            if request.path.startswith('/api/'):
                return jsonify({'success': False, 'error': 'Authentication required'}), 401
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def role_required(allowed_roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                # Check if this is an API request
                if request.path.startswith('/api/'):
                    return jsonify({'success': False, 'error': 'Authentication required'}), 401
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('login'))

            if session.get('role') not in allowed_roles:
                # Check if this is an API request
                if request.path.startswith('/api/'):
                    return jsonify({'success': False, 'error': 'Insufficient permissions'}), 403
                flash('You do not have permission to access this page.', 'error')
                return redirect(url_for('dashboard'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Routes
@app.route('/')
def home():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT id, password_hash, role FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()
        
        if user and check_password_hash(user[1], password):
            session['user_id'] = user[0]
            session['username'] = username
            session['role'] = user[2]
            
            # Update last login
            conn = sqlite3.connect(DATABASE_PATH)
            cursor = conn.cursor()
            cursor.execute('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', (user[0],))
            conn.commit()
            conn.close()
            
            flash(f'Welcome, {username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out successfully.', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    role = session.get('role')
    
    if role in ['Executor 1', 'Executor 2', 'Executor 3']:
        return redirect(url_for('executor_dashboard'))
    elif role == 'Cross-Checker':
        return redirect(url_for('crosschecker_dashboard'))
    elif role == 'Editor':
        return redirect(url_for('editor_dashboard'))
    elif role == 'Admin':
        return redirect(url_for('admin_dashboard'))
    else:
        flash('Unknown role', 'error')
        return redirect(url_for('login'))

@app.route('/executor')
@login_required
@role_required(['Executor 1', 'Executor 2', 'Executor 3'])
def executor_dashboard():
    # Get statistics for the current executor
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    current_executor_id = session.get('user_id')

    # Total pending files for this executor (items in verification queue that came from this executor)
    cursor.execute('''
        SELECT COUNT(*)
        FROM processing_queue pq
        JOIN verification_queue vq ON pq.id = vq.processing_id
        WHERE pq.executor_id = ? AND vq.status = 'pending'
    ''', (current_executor_id,))
    pending_count = cursor.fetchone()[0]

    # Files completed yesterday by this executor (submitted to crosschecker)
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT COUNT(*)
        FROM processing_queue
        WHERE executor_id = ? AND DATE(created_at) = ?
    ''', (current_executor_id, yesterday))
    yesterday_count = cursor.fetchone()[0]

    # Daily average (last 7 days) for this executor
    week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT COUNT(*)
        FROM processing_queue
        WHERE executor_id = ? AND DATE(created_at) >= ?
    ''', (current_executor_id, week_ago))
    week_count = cursor.fetchone()[0]
    daily_average = round(week_count / 7, 1)

    # Additional stats for better dashboard
    # Total files processed by this executor (all time)
    cursor.execute('SELECT COUNT(*) FROM processing_queue WHERE executor_id = ?', (current_executor_id,))
    total_processed = cursor.fetchone()[0]

    # Files verified (approved by crosschecker) for this executor
    cursor.execute('SELECT COUNT(*) FROM processing_queue WHERE executor_id = ? AND status = "verified"', (current_executor_id,))
    verified_count = cursor.fetchone()[0]

    # Files rejected by crosschecker for this executor
    cursor.execute('SELECT COUNT(*) FROM processing_queue WHERE executor_id = ? AND status = "rejected"', (current_executor_id,))
    rejected_count = cursor.fetchone()[0]

    conn.close()

    return render_template('executor_dashboard.html',
                         pending_count=pending_count,
                         yesterday_count=yesterday_count,
                         daily_average=daily_average,
                         total_processed=total_processed,
                         verified_count=verified_count,
                         rejected_count=rejected_count)

@app.route('/api/executor/metrics', methods=['GET'])
@login_required
@role_required(['Executor 1', 'Executor 2', 'Executor 3'])
def get_executor_metrics():
    """Get real-time metrics for the current executor"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        current_executor_id = session.get('user_id')

        # Total pending files for this executor
        cursor.execute('''
            SELECT COUNT(*)
            FROM processing_queue pq
            JOIN verification_queue vq ON pq.id = vq.processing_id
            WHERE pq.executor_id = ? AND vq.status = 'pending'
        ''', (current_executor_id,))
        pending_count = cursor.fetchone()[0]

        # Files completed yesterday by this executor
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        cursor.execute('''
            SELECT COUNT(*)
            FROM processing_queue
            WHERE executor_id = ? AND DATE(created_at) = ?
        ''', (current_executor_id, yesterday))
        yesterday_count = cursor.fetchone()[0]

        # Daily average (last 7 days) for this executor
        week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        cursor.execute('''
            SELECT COUNT(*)
            FROM processing_queue
            WHERE executor_id = ? AND DATE(created_at) >= ?
        ''', (current_executor_id, week_ago))
        week_count = cursor.fetchone()[0]
        daily_average = round(week_count / 7, 1)

        # Additional stats
        cursor.execute('SELECT COUNT(*) FROM processing_queue WHERE executor_id = ?', (current_executor_id,))
        total_processed = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM processing_queue WHERE executor_id = ? AND status = "verified"', (current_executor_id,))
        verified_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM processing_queue WHERE executor_id = ? AND status = "rejected"', (current_executor_id,))
        rejected_count = cursor.fetchone()[0]

        conn.close()

        return jsonify({
            'success': True,
            'pending_count': pending_count,
            'yesterday_count': yesterday_count,
            'daily_average': daily_average,
            'total_processed': total_processed,
            'verified_count': verified_count,
            'rejected_count': rejected_count,
            'updated_at': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"EXECUTOR_METRICS | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/crosschecker')
@login_required
@role_required(['Cross-Checker'])
def crosschecker_dashboard():
    # Get folders pending verification
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Pending verifications
    cursor.execute('''
        SELECT pq.id, pq.folder_name, pq.folder_path, pq.metadata,
               COALESCE(pq.processed_at, pq.created_at, CURRENT_TIMESTAMP) as processed_at,
               u.username
        FROM processing_queue pq
        JOIN users u ON pq.executor_id = u.id
        JOIN verification_queue vq ON pq.id = vq.processing_id
        WHERE pq.status = "completed" AND vq.status = "pending"
        ORDER BY processed_at DESC
    ''')

    pending_verifications_raw = cursor.fetchall()

    # Convert timestamps to local time
    pending_verifications = []
    for item in pending_verifications_raw:
        item_list = list(item)
        # Convert timestamp (item[4]) to local time
        item_list[4] = convert_utc_to_local(item[4])
        pending_verifications.append(item_list)

    # Completed verifications (approved/rejected but not deleted)
    cursor.execute('''
        SELECT pq.id, pq.folder_name, pq.folder_path, pq.metadata, vq.verified_at, u.username
        FROM processing_queue pq
        JOIN verification_queue vq ON pq.id = vq.processing_id
        JOIN users u ON pq.executor_id = u.id
        WHERE vq.status IN ("approved", "rejected", "verified")
        ORDER BY vq.verified_at DESC
        LIMIT 50
    ''')

    completed_verifications = cursor.fetchall()
    conn.close()

    return render_template('crosschecker_dashboard.html',
                         pending_verifications=pending_verifications,
                         completed_verifications=completed_verifications)

@app.route('/editor')
@login_required
@role_required(['Editor'])
def editor_dashboard():
    # Get editing queue items
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    cursor.execute('''
        SELECT eq.id, eq.verification_id, eq.status, eq.created_at, eq.started_at, eq.notes,
               pq.folder_name, pq.metadata, vq.final_folder_path, u.username as executor
        FROM editing_queue eq
        JOIN verification_queue vq ON eq.verification_id = vq.id
        JOIN processing_queue pq ON vq.processing_id = pq.id
        JOIN users u ON pq.executor_id = u.id
        WHERE eq.status IN ('pending', 'in_progress')
        ORDER BY eq.created_at ASC
    ''')

    editing_items_raw = cursor.fetchall()

    # Parse metadata for each item
    editing_items = []
    for item in editing_items_raw:
        # Convert tuple to list for modification
        item_list = list(item)
        # Parse metadata JSON (item[7] is metadata)
        if item[7]:
            try:
                metadata = json.loads(item[7])
                item_list[7] = metadata
            except:
                item_list[7] = {}
        else:
            item_list[7] = {}

        # Convert timestamps to local time
        # item[3] is created_at, item[4] is started_at
        item_list[3] = convert_utc_to_local(item[3]) if item[3] else None
        item_list[4] = convert_utc_to_local(item[4]) if item[4] else None

        editing_items.append(item_list)

    # Get completed items count
    cursor.execute('SELECT COUNT(*) FROM editing_queue WHERE status = "completed"')
    completed_count = cursor.fetchone()[0]

    # Get today's completed count
    today = datetime.now().strftime('%Y-%m-%d')
    cursor.execute('SELECT COUNT(*) FROM editing_queue WHERE status = "completed" AND DATE(completed_at) = ?', (today,))
    today_completed = cursor.fetchone()[0]

    conn.close()

    return render_template('editor_dashboard.html',
                         editing_items=editing_items,
                         completed_count=completed_count,
                         today_completed=today_completed)

@app.route('/admin')
@login_required
@role_required(['Admin'])
def admin_dashboard():
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Overall Summary Statistics
    stats = {}

    # Total files processed by each executor
    cursor.execute('''
        SELECT u.username, COUNT(pq.id) as count
        FROM users u
        LEFT JOIN processing_queue pq ON u.id = pq.executor_id
        WHERE u.role IN ('Executor 1', 'Executor 2', 'Executor 3')
        GROUP BY u.id, u.username
    ''')
    executor_stats = cursor.fetchall()
    stats['executor_stats'] = executor_stats

    # Cross-checker validation stats
    cursor.execute('SELECT COUNT(*) FROM verification_queue WHERE status = "verified"')
    stats['validated_count'] = cursor.fetchone()[0]

    # Editor queue stats
    cursor.execute('SELECT COUNT(*) FROM editing_queue WHERE status = "pending"')
    stats['editor_pending'] = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM editing_queue WHERE status = "completed"')
    stats['editor_completed'] = cursor.fetchone()[0]

    # Total processed files
    cursor.execute('SELECT COUNT(*) FROM processing_queue')
    stats['total_processed'] = cursor.fetchone()[0]

    # Recent activity (last 10 actions)
    cursor.execute('''
        SELECT sl.action, sl.details, sl.timestamp, u.username
        FROM system_logs sl
        JOIN users u ON sl.user_id = u.id
        ORDER BY sl.timestamp DESC
        LIMIT 10
    ''')
    recent_activity = cursor.fetchall()

    # Performance data for charts
    # Daily processing by executors (last 7 days)
    cursor.execute('''
        SELECT DATE(pq.created_at) as date, u.username, COUNT(pq.id) as count
        FROM processing_queue pq
        JOIN users u ON pq.executor_id = u.id
        WHERE pq.created_at >= date('now', '-7 days')
        GROUP BY DATE(pq.created_at), u.username
        ORDER BY date DESC
    ''')
    daily_processing = cursor.fetchall()

    # Cross-checker activity trends (last 7 days)
    cursor.execute('''
        SELECT DATE(vq.verified_at) as date, COUNT(vq.id) as count
        FROM verification_queue vq
        WHERE vq.verified_at >= date('now', '-7 days') AND vq.status = 'verified'
        GROUP BY DATE(vq.verified_at)
        ORDER BY date DESC
    ''')
    crosschecker_activity = cursor.fetchall()

    # Editor progress (last 7 days)
    cursor.execute('''
        SELECT DATE(eq.completed_at) as date, COUNT(eq.id) as count
        FROM editing_queue eq
        WHERE eq.completed_at >= date('now', '-7 days') AND eq.status = 'completed'
        GROUP BY DATE(eq.completed_at)
        ORDER BY date DESC
    ''')
    editor_progress = cursor.fetchall()

    # All users for management
    cursor.execute('SELECT id, username, role, created_at FROM users ORDER BY role, username')
    all_users = cursor.fetchall()

    conn.close()

    return render_template('admin_dashboard.html',
                         stats=stats,
                         recent_activity=recent_activity,
                         daily_processing=daily_processing,
                         crosschecker_activity=crosschecker_activity,
                         editor_progress=editor_progress,
                         all_users=all_users)

@app.route('/api/admin/performance', methods=['GET'])
@login_required
@role_required(['Admin'])
def get_admin_performance_data():
    """Get real-time performance data for admin dashboard charts"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Daily processing by executors (last 7 days) with proper date formatting
        cursor.execute('''
            SELECT DATE(pq.created_at) as date, u.username, COUNT(pq.id) as count
            FROM processing_queue pq
            JOIN users u ON pq.executor_id = u.id
            WHERE pq.created_at >= date('now', '-7 days') AND u.role IN ('Executor 1', 'Executor 2', 'Executor 3')
            GROUP BY DATE(pq.created_at), u.username, u.role
            ORDER BY date ASC, u.role
        ''')
        daily_processing_raw = cursor.fetchall()

        # Cross-checker activity trends (last 7 days)
        cursor.execute('''
            SELECT DATE(vq.verified_at) as date,
                   COUNT(CASE WHEN vq.status = 'verified' THEN 1 END) as verified_count,
                   COUNT(CASE WHEN vq.status = 'rejected' THEN 1 END) as rejected_count,
                   AVG(CASE WHEN vq.verified_at IS NOT NULL AND pq.created_at IS NOT NULL
                       THEN (julianday(vq.verified_at) - julianday(pq.created_at)) * 24 END) as avg_processing_hours
            FROM verification_queue vq
            JOIN processing_queue pq ON vq.processing_id = pq.id
            WHERE vq.verified_at >= date('now', '-7 days')
            GROUP BY DATE(vq.verified_at)
            ORDER BY date ASC
        ''')
        crosschecker_activity_raw = cursor.fetchall()

        # Editor progress (last 7 days)
        cursor.execute('''
            SELECT DATE(eq.completed_at) as date,
                   COUNT(eq.id) as completed_count,
                   AVG(CASE WHEN eq.completed_at IS NOT NULL AND eq.started_at IS NOT NULL
                       THEN (julianday(eq.completed_at) - julianday(eq.started_at)) * 24 END) as avg_editing_hours
            FROM editing_queue eq
            WHERE eq.completed_at >= date('now', '-7 days') AND eq.status = 'completed'
            GROUP BY DATE(eq.completed_at)
            ORDER BY date ASC
        ''')
        editor_progress_raw = cursor.fetchall()

        # Overall workflow status
        cursor.execute('''
            SELECT
                COUNT(CASE WHEN pq.status = 'completed' THEN 1 END) as pending_crosscheck,
                COUNT(CASE WHEN pq.status = 'verified' THEN 1 END) as verified,
                COUNT(CASE WHEN pq.status = 'rejected' THEN 1 END) as rejected,
                COUNT(CASE WHEN eq.status = 'pending' THEN 1 END) as pending_editing,
                COUNT(CASE WHEN eq.status = 'completed' THEN 1 END) as editing_completed
            FROM processing_queue pq
            LEFT JOIN verification_queue vq ON pq.id = vq.processing_id
            LEFT JOIN editing_queue eq ON vq.id = eq.verification_id
        ''')
        workflow_status_raw = cursor.fetchone()

        conn.close()

        # Process the data for frontend consumption
        # Generate last 7 days labels
        from datetime import datetime, timedelta
        dates = []
        for i in range(6, -1, -1):
            date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            dates.append(date)

        # Process daily processing data
        daily_processing = {
            'labels': [datetime.strptime(d, '%Y-%m-%d').strftime('%a') for d in dates],
            'datasets': {
                'executor1': [0] * 7,
                'executor2': [0] * 7,
                'executor3': [0] * 7
            }
        }

        for date, username, count in daily_processing_raw:
            if date in dates:
                date_index = dates.index(date)
                if 'executor1' in username.lower():
                    daily_processing['datasets']['executor1'][date_index] = count
                elif 'executor2' in username.lower():
                    daily_processing['datasets']['executor2'][date_index] = count
                elif 'executor3' in username.lower():
                    daily_processing['datasets']['executor3'][date_index] = count

        # Process crosschecker activity data
        crosschecker_activity = {
            'labels': [datetime.strptime(d, '%Y-%m-%d').strftime('%a') for d in dates],
            'verified': [0] * 7,
            'rejected': [0] * 7,
            'avg_hours': [0] * 7
        }

        for date, verified, rejected, avg_hours in crosschecker_activity_raw:
            if date in dates:
                date_index = dates.index(date)
                crosschecker_activity['verified'][date_index] = verified or 0
                crosschecker_activity['rejected'][date_index] = rejected or 0
                crosschecker_activity['avg_hours'][date_index] = round(avg_hours or 0, 1)

        # Process editor progress data
        editor_progress = {
            'labels': [datetime.strptime(d, '%Y-%m-%d').strftime('%a') for d in dates],
            'completed': [0] * 7,
            'avg_hours': [0] * 7
        }

        for date, completed, avg_hours in editor_progress_raw:
            if date in dates:
                date_index = dates.index(date)
                editor_progress['completed'][date_index] = completed or 0
                editor_progress['avg_hours'][date_index] = round(avg_hours or 0, 1)

        # Process workflow status
        workflow_status = {
            'pending_crosscheck': workflow_status_raw[0] or 0,
            'verified': workflow_status_raw[1] or 0,
            'rejected': workflow_status_raw[2] or 0,
            'pending_editing': workflow_status_raw[3] or 0,
            'editing_completed': workflow_status_raw[4] or 0
        }

        return jsonify({
            'success': True,
            'daily_processing': daily_processing,
            'crosschecker_activity': crosschecker_activity,
            'editor_progress': editor_progress,
            'workflow_status': workflow_status,
            'updated_at': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"ADMIN_PERFORMANCE | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

# API Routes
@app.route('/api/search_google_sheets', methods=['POST'])
@login_required
@role_required(['Executor 1', 'Executor 2', 'Executor 3'])
def search_google_sheets():
    """Search for video ID in Google Sheets"""
    try:
        data = request.get_json()
        video_id = data.get('video_id')

        if not video_id:
            return jsonify({'success': False, 'error': 'Video ID is required'})

        client = init_google_sheets()
        if not client:
            return jsonify({'success': False, 'error': 'Google Sheets not available'})

        spreadsheet = client.open_by_key(GOOGLE_SHEET_ID)

        # Search in each sheet
        for sheet_name in SHEET_NAMES:
            try:
                worksheet = spreadsheet.worksheet(sheet_name)
                records = worksheet.get_all_records()

                # Search in column C (index 2)
                for i, record in enumerate(records):
                    if len(record) > 2:
                        # Get the third column value (Column C)
                        column_c_value = list(record.values())[2] if len(list(record.values())) > 2 else ''

                        if str(column_c_value).strip() == str(video_id).strip():
                            # Found match, extract data
                            values = list(record.values())

                            # Convert ISO date format
                            date_value = values[1] if len(values) > 1 else ''
                            formatted_date = convert_iso_date(date_value)

                            # Convert duration format
                            duration_value = values[8] if len(values) > 8 else ''
                            formatted_duration = convert_duration(duration_value)

                            # Determine platform
                            platform = get_platform_name(sheet_name)

                            result = {
                                'success': True,
                                'found': True,
                                'data': {
                                    'title': values[3] if len(values) > 3 else '',
                                    'date': formatted_date,
                                    'description': values[6] if len(values) > 6 else '',
                                    'url': values[7] if len(values) > 7 else '',
                                    'duration': formatted_duration,
                                    'transcription_file': values[11] if len(values) > 11 else '',
                                    'platform': platform,
                                    'sheet': sheet_name
                                }
                            }
                            return jsonify(result)

            except Exception as e:
                print(f"Error searching sheet {sheet_name}: {e}")
                continue

        # Not found in any sheet
        return jsonify({'success': True, 'found': False, 'message': 'Video ID not found in any sheet'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_folder', methods=['POST'])
@login_required
@role_required(['Executor 1', 'Executor 2', 'Executor 3'])
@log_action("METADATA_SUBMISSION")
def process_folder():
    """Process folder with metadata"""
    try:
        data = request.get_json()

        # Log metadata submission details
        folder_name = data.get('folder_name')
        folder_path = data.get('folder_path')
        metadata_to_store = data.get('metadata') or data
        video_id = metadata_to_store.get('video_id', 'N/A')
        soft_rename = metadata_to_store.get('soft_renamed', 'N/A')

        logger.info(f"METADATA_SUBMISSION | Executor: {session.get('username')} | Folder: {folder_name} | Video ID: {video_id} | Soft Rename: {soft_rename}")

        # Save to processing queue
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO processing_queue (folder_path, folder_name, executor_id, metadata, status)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            folder_path,
            folder_name,
            session.get('user_id'),
            json.dumps(metadata_to_store),
            'completed'
        ))

        processing_id = cursor.lastrowid

        # Add to verification queue
        cursor.execute('''
            INSERT INTO verification_queue (processing_id, status)
            VALUES (?, ?)
        ''', (processing_id, 'pending'))

        # Log to system logs
        log_system_action(cursor, session.get('user_id'), 'METADATA_SUBMISSION',
                         f'Folder: {folder_name}, Video ID: {video_id}, Processing ID: {processing_id}')

        conn.commit()
        conn.close()

        logger.info(f"METADATA_SUBMISSION | Processing ID: {processing_id} | SUCCESS")

        return jsonify({'success': True, 'message': 'Folder processed and sent for verification', 'processing_id': processing_id})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/browse_directory', methods=['POST'])
@login_required
@role_required(['Cross-Checker', 'Editor', 'Admin'])
def browse_directory():
    """Browse directory contents for file selection - RESTRICTED TO E:\ DRIVE ONLY"""
    try:
        data = request.get_json()
        path = data.get('path', 'E:\\')

        logger.info(f"BROWSE_DIRECTORY | Path: {path} | User: {session.get('username')}")

        # SECURITY: Restrict all browsing to E:\ drive only
        ALLOWED_ROOT = 'E:\\'

        # Normalize path and ensure it starts with E:\
        if not path or path == '/' or path == '' or path == 'drives':
            path = ALLOWED_ROOT

        # Convert forward slashes to backslashes for Windows
        path = path.replace('/', '\\')

        # Ensure path starts with E:\ (case insensitive)
        if not path.upper().startswith('E:\\'):
            logger.warning(f"BROWSE_DIRECTORY | SECURITY | Attempted access outside E:\\ drive: {path}")
            path = ALLOWED_ROOT

        # Additional security check: resolve path and ensure it's still within E:\
        try:
            resolved_path = os.path.abspath(path)
            if not resolved_path.upper().startswith('E:\\'):
                logger.warning(f"BROWSE_DIRECTORY | SECURITY | Resolved path outside E:\\ drive: {resolved_path}")
                path = ALLOWED_ROOT
            else:
                path = resolved_path
        except:
            path = ALLOWED_ROOT

        # If requesting root or E:\ specifically, start at E:\
        if path.upper() in ['E:', 'E:\\']:
            path = ALLOWED_ROOT

        # Final security validation: Ensure we're still within E:\ drive
        if not path.upper().startswith('E:\\'):
            logger.error(f"BROWSE_DIRECTORY | SECURITY VIOLATION | Final check failed for path: {path}")
            return jsonify({'success': False, 'error': 'Access restricted to E:\\ drive only'})

        # Check if directory exists
        if not os.path.exists(path):
            return jsonify({'success': False, 'error': 'Directory does not exist'})

        if not os.path.isdir(path):
            return jsonify({'success': False, 'error': 'Path is not a directory'})

        # Get directory contents
        folders = []
        files = []

        try:
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    folders.append(item)
                elif os.path.isfile(item_path):
                    files.append(item)
        except PermissionError:
            return jsonify({'success': False, 'error': 'Permission denied to access directory'})

        # Sort items
        folders.sort()
        files.sort()

        return jsonify({
            'success': True,
            'path': path,
            'folders': folders,
            'files': files
        })

    except Exception as e:
        logger.error(f"BROWSE_DIRECTORY | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/create_directory', methods=['POST'])
@login_required
@role_required(['Cross-Checker', 'Admin'])
def create_directory():
    """Create a new directory"""
    try:
        data = request.get_json()
        parent_path = data.get('parent_path', '')
        folder_name = data.get('folder_name', '')

        if not parent_path or not folder_name:
            return jsonify({'success': False, 'error': 'Parent path and folder name are required'})

        # Security check
        allowed_roots = [
            'E:\\Dashboard\\Backlog Edited Mapping Project',
            'E:\\complete',
            'E:\\final'
        ]

        path_allowed = any(parent_path.startswith(root) for root in allowed_roots)
        if not path_allowed:
            return jsonify({'success': False, 'error': 'Cannot create directory in this location'})

        new_dir_path = os.path.join(parent_path, folder_name)

        if os.path.exists(new_dir_path):
            return jsonify({'success': False, 'error': 'Directory already exists'})

        os.makedirs(new_dir_path)

        logger.info(f"CREATE_DIRECTORY | Created: {new_dir_path} | User: {session.get('username')}")

        return jsonify({'success': True, 'message': f'Directory "{folder_name}" created successfully'})

    except Exception as e:
        logger.error(f"CREATE_DIRECTORY | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_audio_batch', methods=['POST'])
@login_required
@role_required(['Cross-Checker', 'Admin'])
def process_audio_batch():
    """Process audio extraction for a batch of items"""
    try:
        data = request.get_json()
        items = data.get('items', [])

        if not items:
            return jsonify({'success': False, 'error': 'No items provided for processing'})

        logger.info(f"AUDIO_BATCH_PROCESSING | Starting batch of {len(items)} items | User: {session.get('username')}")

        results = []

        for item in items:
            try:
                result = process_single_audio_item(item)
                results.append(result)

            except Exception as e:
                logger.error(f"AUDIO_PROCESSING | ERROR | Item: {item.get('folderName')} | Error: {str(e)}")
                results.append({
                    'success': False,
                    'folderName': item.get('folderName'),
                    'error': str(e)
                })

        successful_count = sum(1 for r in results if r.get('success'))

        logger.info(f"AUDIO_BATCH_PROCESSING | Completed | Success: {successful_count}/{len(items)}")

        return jsonify({
            'success': True,
            'results': results,
            'summary': {
                'total': len(items),
                'successful': successful_count,
                'failed': len(items) - successful_count
            }
        })

    except Exception as e:
        logger.error(f"AUDIO_BATCH_PROCESSING | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

def get_latest_metadata_from_database(processing_id, folder_name):
    """Fetch the latest metadata from the database for batch processing"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # First try to get from processing_queue using processing_id
        if processing_id:
            cursor.execute('SELECT metadata FROM processing_queue WHERE id = ?', (processing_id,))
            result = cursor.fetchone()
            if result and result[0]:
                metadata_json = result[0]
                metadata = json.loads(metadata_json) if isinstance(metadata_json, str) else metadata_json
                logger.info(f"METADATA_FETCH | SUCCESS | Found latest metadata for processing_id: {processing_id}")
                conn.close()
                return metadata

        # Fallback: try to get from processing_queue using folder_name
        cursor.execute('SELECT metadata FROM processing_queue WHERE folder_name = ? ORDER BY created_at DESC LIMIT 1', (folder_name,))
        result = cursor.fetchone()
        if result and result[0]:
            metadata_json = result[0]
            metadata = json.loads(metadata_json) if isinstance(metadata_json, str) else metadata_json
            logger.info(f"METADATA_FETCH | SUCCESS | Found latest metadata for folder: {folder_name}")
            conn.close()
            return metadata

        # If not found in processing_queue, try verification_queue
        cursor.execute('SELECT metadata FROM verification_queue WHERE folder_name = ? ORDER BY created_at DESC LIMIT 1', (folder_name,))
        result = cursor.fetchone()
        if result and result[0]:
            metadata_json = result[0]
            metadata = json.loads(metadata_json) if isinstance(metadata_json, str) else metadata_json
            logger.info(f"METADATA_FETCH | SUCCESS | Found latest metadata in verification_queue for folder: {folder_name}")
            conn.close()
            return metadata

        logger.warning(f"METADATA_FETCH | WARNING | No metadata found for processing_id: {processing_id}, folder: {folder_name}")
        conn.close()
        return None

    except Exception as e:
        logger.error(f"METADATA_FETCH | ERROR | {str(e)}")
        return None

def process_single_audio_item(item):
    """Process a single audio item with REAL backend operations"""
    try:
        folder_name = item.get('folderName')
        mov_file_path = item.get('movFilePath')
        audio_output_path = item.get('audioOutputPath')
        source_folder_path = item.get('sourceFolderPath')
        processing_id = item.get('processingId')

        # Check for both field names for backward compatibility
        destination_folder_path = item.get('final_folder_path') or item.get('destinationFolderPath')

        if not destination_folder_path:
            logger.error(f"AUDIO_PROCESSING | ERROR | No destination folder path provided")
            raise Exception("No destination folder path provided. Please select a destination folder.")

        # CRITICAL FIX: Fetch latest metadata from database instead of using old cached metadata
        metadata = get_latest_metadata_from_database(processing_id, folder_name)
        if not metadata:
            logger.warning(f"AUDIO_PROCESSING | WARNING | Could not fetch latest metadata, using cached metadata")
            metadata = item.get('metadata', {})

        logger.info(f"AUDIO_PROCESSING | Starting REAL processing | Folder: {folder_name}")
        logger.info(f"AUDIO_PROCESSING | DEBUG | Received mov_file_path: '{mov_file_path}'")
        logger.info(f"AUDIO_PROCESSING | DEBUG | Received audio_output_path: '{audio_output_path}'")
        logger.info(f"AUDIO_PROCESSING | DEBUG | Received source_folder_path: '{source_folder_path}'")
        logger.info(f"AUDIO_PROCESSING | DEBUG | Received destination_folder_path: '{destination_folder_path}'")
        logger.info(f"AUDIO_PROCESSING | METADATA | Using latest metadata from database for processing")

        # Step 1: REAL Audio Extraction with FFmpeg
        audio_code = metadata.get('audio_code', 'UNKNOWN')
        soft_renamed = metadata.get('soft_renamed', folder_name)

        logger.info(f"AUDIO_PROCESSING | METADATA VALUES | Audio Code: {audio_code}")
        logger.info(f"AUDIO_PROCESSING | METADATA VALUES | Soft Renamed: {soft_renamed}")
        logger.info(f"AUDIO_PROCESSING | METADATA VALUES | Original Folder: {folder_name}")

        # Create the exact filename format: AudioCode_SoftRenamedName.wav
        audio_filename = f"{audio_code}_{soft_renamed}.wav"
        logger.info(f"AUDIO_PROCESSING | FILENAME GENERATION | Generated filename: {audio_filename}")

        # Full path for the output audio file
        audio_output_full_path = os.path.join(audio_output_path, audio_filename)

        # Ensure output directory exists
        os.makedirs(audio_output_path, exist_ok=True)

        # REAL FFmpeg audio extraction with ACTUAL VERIFICATION
        logger.info(f"FFMPEG_EXTRACTION | Starting REAL FFmpeg processing")
        logger.info(f"FFMPEG_EXTRACTION | Input file: {mov_file_path}")
        logger.info(f"FFMPEG_EXTRACTION | Output file: {audio_output_full_path}")

        # STEP 1: Verify source file exists
        if not os.path.exists(mov_file_path):
            error_msg = f"Source .mov file does not exist: {mov_file_path}"
            logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
            raise Exception(error_msg)

        # Check source file size
        source_size = os.path.getsize(mov_file_path)
        logger.info(f"FFMPEG_EXTRACTION | Source file size: {source_size} bytes")

        if source_size == 0:
            error_msg = f"Source .mov file is empty: {mov_file_path}"
            logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 2: Execute FFmpeg command with local FFmpeg path
        # Try to find FFmpeg in the project directory first
        project_ffmpeg = os.path.join(os.getcwd(), 'ffmpeg', 'ffmpeg.exe')
        ffmpeg_executable = project_ffmpeg if os.path.exists(project_ffmpeg) else 'ffmpeg'

        logger.info(f"FFMPEG_EXTRACTION | Using FFmpeg: {ffmpeg_executable}")

        ffmpeg_command = [
            ffmpeg_executable, '-i', mov_file_path,
            '-vn',  # No video
            '-acodec', 'pcm_s16le',  # High quality audio codec
            '-ar', '44100',  # Sample rate
            '-ac', '2',  # Stereo
            '-threads', '4',  # Use multiple threads for faster processing
            '-preset', 'fast',  # Fast encoding preset
            audio_output_full_path,
            '-y'  # Overwrite output file
        ]

        logger.info(f"FFMPEG_EXTRACTION | Executing command: {' '.join(ffmpeg_command)}")

        try:
            # Start FFmpeg process with shorter timeout and better error handling
            logger.info(f"FFMPEG_EXTRACTION | Starting FFmpeg process...")

            process = subprocess.Popen(
                ffmpeg_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            # Wait for process with shorter timeout (2 minutes for small files)
            try:
                stdout, stderr = process.communicate(timeout=120)
                return_code = process.returncode

                logger.info(f"FFMPEG_EXTRACTION | Process completed with return code: {return_code}")
                logger.info(f"FFMPEG_EXTRACTION | FFmpeg stderr: {stderr}")

                # STEP 3: Check FFmpeg return code
                if return_code != 0:
                    error_msg = f"FFmpeg failed with return code {return_code}: {stderr}"
                    logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
                    raise Exception(error_msg)

                logger.info(f"FFMPEG_EXTRACTION | FFmpeg completed successfully")

            except subprocess.TimeoutExpired:
                logger.error(f"FFMPEG_EXTRACTION | Process timed out, killing...")
                process.kill()
                stdout, stderr = process.communicate()
                error_msg = f"FFmpeg process timed out after 2 minutes. This may indicate a problem with the input file or FFmpeg configuration."
                logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
                raise Exception(error_msg)

        except FileNotFoundError:
            error_msg = "FFmpeg not found. Please install FFmpeg and add it to PATH"
            logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"FFmpeg execution error: {str(e)}"
            logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 4: VERIFY audio file was actually created
        if not os.path.exists(audio_output_full_path):
            error_msg = f"Audio file was not created despite FFmpeg success: {audio_output_full_path}"
            logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 5: Verify audio file is not empty
        audio_file_size = os.path.getsize(audio_output_full_path)
        if audio_file_size == 0:
            error_msg = f"Audio file was created but is empty: {audio_output_full_path}"
            logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 6: Verify audio file is reasonable size (at least 1KB)
        if audio_file_size < 1024:
            error_msg = f"Audio file is too small ({audio_file_size} bytes), likely corrupted: {audio_output_full_path}"
            logger.error(f"FFMPEG_EXTRACTION | FAILED | {error_msg}")
            raise Exception(error_msg)

        logger.info(f"FFMPEG_EXTRACTION | SUCCESS | Audio file created and verified")
        logger.info(f"FFMPEG_EXTRACTION | Audio file size: {audio_file_size} bytes")
        logger.info(f"FFMPEG_EXTRACTION | Audio file path: {audio_output_full_path}")

        # Step 2: REAL Folder Movement and Renaming with ACTUAL VERIFICATION
        # Use the source folder path provided by the user
        original_folder_path = source_folder_path
        final_destination_path = destination_folder_path

        logger.info(f"FOLDER_MOVEMENT | Starting REAL folder operations")
        logger.info(f"FOLDER_MOVEMENT | Target folder name: {folder_name}")
        logger.info(f"FOLDER_MOVEMENT | Source folder: {original_folder_path}")
        logger.info(f"FOLDER_MOVEMENT | Destination folder: {final_destination_path}")
        logger.info(f"FOLDER_MOVEMENT | Soft renamed name: {soft_renamed}")

        if not final_destination_path or not soft_renamed:
            error_msg = "Destination folder path or soft renamed name not provided"
            logger.error(f"FOLDER_MOVEMENT | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 1: Verify original folder exists and is the correct one
        if not os.path.exists(original_folder_path):
            error_msg = f"Original folder does not exist: {original_folder_path}"
            logger.error(f"FOLDER_MOVEMENT | FAILED | {error_msg}")
            raise Exception(error_msg)

        # Verify this is the correct project folder
        folder_basename = os.path.basename(original_folder_path)
        if folder_name not in folder_basename:
            logger.warning(f"FOLDER_MOVEMENT | WARNING | Folder name mismatch: expected '{folder_name}' in '{folder_basename}'")
            # Try to find the correct folder
            parent_dir = os.path.dirname(original_folder_path)
            correct_folder = None
            try:
                for item in os.listdir(parent_dir):
                    item_path = os.path.join(parent_dir, item)
                    if os.path.isdir(item_path) and folder_name in item:
                        correct_folder = item_path
                        break

                if correct_folder:
                    logger.info(f"FOLDER_MOVEMENT | Found correct folder: {correct_folder}")
                    original_folder_path = correct_folder
                else:
                    logger.warning(f"FOLDER_MOVEMENT | Could not find folder matching '{folder_name}', proceeding with: {original_folder_path}")
            except (PermissionError, OSError):
                logger.warning(f"FOLDER_MOVEMENT | Could not search for correct folder, proceeding with: {original_folder_path}")

        logger.info(f"FOLDER_MOVEMENT | Verified source folder: {original_folder_path}")

        # STEP 2: Create final destination path
        final_folder_full_path = os.path.join(final_destination_path, soft_renamed)
        logger.info(f"FOLDER_MOVEMENT | Full destination path: {final_folder_full_path}")

        # STEP 3: Create destination directory
        try:
            os.makedirs(final_folder_full_path, exist_ok=True)
            logger.info(f"FOLDER_MOVEMENT | Destination directory created")
        except Exception as e:
            error_msg = f"Failed to create destination directory: {str(e)}"
            logger.error(f"FOLDER_MOVEMENT | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 4: Verify destination directory was created
        if not os.path.exists(final_folder_full_path):
            error_msg = f"Destination directory was not created: {final_folder_full_path}"
            logger.error(f"FOLDER_MOVEMENT | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 5: Copy all files from original to destination with verification
        copied_files = []
        failed_files = []

        try:
            for item_name in os.listdir(original_folder_path):
                src_path = os.path.join(original_folder_path, item_name)
                dst_path = os.path.join(final_folder_full_path, item_name)

                if os.path.isfile(src_path):
                    # Copy file
                    shutil.copy2(src_path, dst_path)

                    # VERIFY file was copied
                    if os.path.exists(dst_path):
                        src_size = os.path.getsize(src_path)
                        dst_size = os.path.getsize(dst_path)

                        if src_size == dst_size:
                            copied_files.append(item_name)
                            logger.info(f"FOLDER_MOVEMENT | Successfully copied: {item_name} ({src_size} bytes)")
                        else:
                            failed_files.append(f"{item_name} (size mismatch: {src_size} vs {dst_size})")
                            logger.error(f"FOLDER_MOVEMENT | Size mismatch for {item_name}: {src_size} vs {dst_size}")
                    else:
                        failed_files.append(f"{item_name} (file not created)")
                        logger.error(f"FOLDER_MOVEMENT | File not created: {dst_path}")

                elif os.path.isdir(src_path):
                    # Copy directory recursively
                    shutil.copytree(src_path, dst_path, dirs_exist_ok=True)

                    # VERIFY directory was copied
                    if os.path.exists(dst_path):
                        copied_files.append(f"{item_name}/ (directory)")
                        logger.info(f"FOLDER_MOVEMENT | Successfully copied directory: {item_name}")
                    else:
                        failed_files.append(f"{item_name}/ (directory not created)")
                        logger.error(f"FOLDER_MOVEMENT | Directory not created: {dst_path}")

        except Exception as e:
            error_msg = f"Error during file copying: {str(e)}"
            logger.error(f"FOLDER_MOVEMENT | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 6: Verify overall success
        if failed_files:
            error_msg = f"Failed to copy {len(failed_files)} items: {', '.join(failed_files)}"
            logger.error(f"FOLDER_MOVEMENT | PARTIAL FAILURE | {error_msg}")
            raise Exception(error_msg)

        if not copied_files:
            error_msg = "No files were copied from original folder"
            logger.error(f"FOLDER_MOVEMENT | FAILED | {error_msg}")
            raise Exception(error_msg)

        logger.info(f"FOLDER_MOVEMENT | SUCCESS | Copied {len(copied_files)} items to: {final_folder_full_path}")

        # STEP 7: DELETE SOURCE FOLDER (to complete the MOVE operation)
        try:
            logger.info(f"FOLDER_MOVEMENT | Deleting source folder: {original_folder_path}")
            shutil.rmtree(original_folder_path)

            # Verify source folder was deleted
            if os.path.exists(original_folder_path):
                error_msg = f"Source folder still exists after deletion attempt: {original_folder_path}"
                logger.error(f"FOLDER_MOVEMENT | FAILED | {error_msg}")
                raise Exception(error_msg)

            logger.info(f"FOLDER_MOVEMENT | SUCCESS | Source folder deleted: {original_folder_path}")
            logger.info(f"FOLDER_MOVEMENT | MOVE OPERATION COMPLETED | Folder moved from {original_folder_path} to {final_folder_full_path}")

        except Exception as e:
            error_msg = f"Failed to delete source folder: {str(e)}"
            logger.error(f"FOLDER_MOVEMENT | FAILED | {error_msg}")
            raise Exception(error_msg)

        # STEP 8: REAL Google Sheets Integration with Enhanced Error Handling
        sheet_success = False
        try:
            logger.info(f"GOOGLE_SHEETS | Starting real Google Sheets integration")
            logger.info(f"GOOGLE_SHEETS | Audio Code: {audio_code}")
            logger.info(f"GOOGLE_SHEETS | Soft Renamed: {soft_renamed}")
            logger.info(f"GOOGLE_SHEETS | Audio Filename: {audio_filename}")

            # Import Google Sheets libraries
            import gspread
            from google.oauth2.service_account import Credentials
            import ssl
            import urllib3
            import httplib2
            logger.info(f"GOOGLE_SHEETS | Libraries imported successfully")

            # Disable SSL warnings and verification for corporate environments
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # Set up credentials with SSL bypass
            SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
            logger.info(f"GOOGLE_SHEETS | Loading credentials from credentials.json")

            if not os.path.exists('credentials.json'):
                raise Exception("credentials.json file not found")

            # Set environment variables to bypass SSL
            os.environ['PYTHONHTTPSVERIFY'] = '0'
            os.environ['CURL_CA_BUNDLE'] = ''

            credentials = Credentials.from_service_account_file('credentials.json', scopes=SCOPES)

            # Create HTTP client with SSL verification disabled
            http = httplib2.Http(disable_ssl_certificate_validation=True)
            gc = gspread.authorize(credentials, http=http)

            logger.info(f"GOOGLE_SHEETS | Credentials loaded and authorized successfully (SSL bypass enabled)")

            # Open the Main Sheet using global constant
            logger.info(f"GOOGLE_SHEETS | Opening sheet with ID: {MAIN_SHEET_ID}")

            spreadsheet = gc.open_by_key(MAIN_SHEET_ID)
            sheet = spreadsheet.sheet1
            logger.info(f"GOOGLE_SHEETS | Sheet opened successfully: {spreadsheet.title}")

            # Prepare data row for Google Sheets
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logger.info(f"GOOGLE_SHEETS | Preparing data row for timestamp: {current_time}")

            sheets_row = [
                metadata.get('ocd_number', ''),
                audio_code,
                metadata.get('video_type', ''),
                metadata.get('component', ''),
                metadata.get('language', ''),
                metadata.get('title', ''),
                metadata.get('description', ''),
                metadata.get('total_duration', ''),
                metadata.get('platform', ''),
                soft_renamed,
                audio_filename,
                audio_output_path,
                final_destination_path,
                current_time,
                session.get('username', 'Cross-Checker'),
                'Audio Processed',
                mov_file_path,
                metadata.get('project_date', ''),
                metadata.get('distribution_type', ''),
                metadata.get('backup_type', ''),
                metadata.get('additional_notes', ''),
                metadata.get('url', '')
            ]

            logger.info(f"GOOGLE_SHEETS | Data row prepared with {len(sheets_row)} columns")
            logger.info(f"GOOGLE_SHEETS | Key data: OCD={metadata.get('ocd_number')}, Audio={audio_filename}")

            # Add row to Google Sheets
            logger.info(f"GOOGLE_SHEETS | Adding row to sheet...")
            sheet.append_row(sheets_row)
            sheet_success = True

            logger.info(f"GOOGLE_SHEETS | SUCCESS | Data added to Main Sheet successfully")
            logger.info(f"GOOGLE_SHEETS | Row added: OCD={metadata.get('ocd_number')}, Audio={audio_filename}")

        except Exception as e:
            logger.error(f"GOOGLE_SHEETS | ERROR | Failed to update Google Sheets: {str(e)}")
            logger.error(f"GOOGLE_SHEETS | Error type: {type(e).__name__}")
            logger.error(f"GOOGLE_SHEETS | Error details: {repr(e)}")

            # Try to get more specific error information
            if hasattr(e, 'response'):
                logger.error(f"GOOGLE_SHEETS | HTTP Response: {e.response}")
            if hasattr(e, 'status_code'):
                logger.error(f"GOOGLE_SHEETS | Status Code: {e.status_code}")

            # Don't fail the entire process for Google Sheets errors
            logger.info(f"GOOGLE_SHEETS | Continuing processing despite Google Sheets error")
            sheet_success = False

        # Step 4: REAL Database Update
        try:
            logger.info(f"DATABASE | Starting real database update")

            conn = sqlite3.connect("archives_mediaflow_pro.db")
            cursor = conn.cursor()

            # Update metadata with processing results
            updated_metadata = metadata.copy()
            updated_metadata.update({
                'audio_filename': audio_filename,
                'audio_output_path': audio_output_full_path,
                'final_folder_path': final_destination_path,
                'processed_at': datetime.now().isoformat(),
                'processed_by': session.get('username'),
                'processing_status': 'audio_completed',
                'google_sheets_updated': sheet_success
            })

            logger.info(f"DATABASE | Google Sheets Update Status: {sheet_success}")

            # Update processing queue status
            cursor.execute('''
                UPDATE processing_queue
                SET status = 'audio_processed',
                    processed_at = CURRENT_TIMESTAMP,
                    metadata = ?
                WHERE folder_name = ?
            ''', (json.dumps(updated_metadata), folder_name))

            # Update verification queue
            cursor.execute('''
                UPDATE verification_queue
                SET status = 'audio_processed'
                WHERE processing_id IN (
                    SELECT id FROM processing_queue WHERE folder_name = ?
                )
            ''', (folder_name,))

            conn.commit()
            conn.close()

            logger.info(f"DATABASE | SUCCESS | Updated database for: {folder_name}")

        except Exception as e:
            logger.error(f"DATABASE | ERROR | {str(e)}")
            logger.info(f"DATABASE | Continuing processing despite database error")

        logger.info(f"AUDIO_PROCESSING | COMPLETED SUCCESSFULLY | Folder: {folder_name}")

        return {
            'success': True,
            'folderName': folder_name,
            'audioFilename': audio_filename,
            'audioOutputPath': audio_output_full_path,
            'finalFolderPath': final_destination_path,
            'movFilePath': mov_file_path,
            'processedAt': datetime.now().isoformat(),
            'processedBy': session.get('username')
        }

    except Exception as e:
        logger.error(f"AUDIO_PROCESSING | CRITICAL ERROR | Folder: {item.get('folderName')} | Error: {str(e)}")
        raise e

@app.route('/api/test_ssl', methods=['GET'])
@login_required
def test_ssl():
    """Test SSL bypass for Google Sheets"""
    try:
        import ssl
        import urllib3

        # Disable SSL warnings
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # Set environment variables
        os.environ['PYTHONHTTPSVERIFY'] = '0'
        os.environ['CURL_CA_BUNDLE'] = ''

        # Monkey patch SSL
        ssl._create_default_https_context = ssl._create_unverified_context

        # Test simple HTTPS request
        import requests
        response = requests.get('https://www.google.com', verify=False, timeout=10)

        return jsonify({
            'success': True,
            'message': f'SSL bypass working. Status: {response.status_code}',
            'ssl_bypassed': True
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'ssl_bypassed': False
        })

@app.route('/api/browse_files', methods=['POST'])
@login_required
def browse_files():
    """Browse files on the server file system - ROLE-BASED RESTRICTIONS"""
    try:
        data = request.get_json()
        path = data.get('path', '')
        file_type = data.get('type', 'all')  # 'video', 'folder', 'all'
        context = data.get('context', '')  # 'validation_workflow' for crosschecker validation

        user_role = session.get('role')
        items = []

        # SECURITY: Role-based path restrictions with context awareness
        if user_role in ['Executor 1', 'Executor 2', 'Executor 3']:
            # Executors are restricted to E:\Work space only
            ALLOWED_ROOT = 'E:\\Work space'
            logger.info(f"FILE_BROWSER | Executor access restricted to: {ALLOWED_ROOT}")
        elif user_role == 'Cross-Checker' and context == 'validation_workflow':
            # Cross-Checker validation workflow is restricted to E:\Work space
            ALLOWED_ROOT = 'E:\\Work space'
            logger.info(f"FILE_BROWSER | Cross-Checker validation workflow restricted to: {ALLOWED_ROOT}")
        else:
            # Other roles (Editor, Admin) and Cross-Checker non-validation can access full E:\ drive
            ALLOWED_ROOT = 'E:\\'
            logger.info(f"FILE_BROWSER | {user_role} access to full E:\\ drive")

        # If no path or root requested, start at the appropriate root
        if not path or path == '' or path == 'drives':
            path = ALLOWED_ROOT
            logger.info(f"FILE_BROWSER | Starting at {ALLOWED_ROOT} for {user_role}")

        # Convert forward slashes to backslashes for Windows
        path = path.replace('/', '\\')

        # Role-specific path validation with context awareness
        if user_role in ['Executor 1', 'Executor 2', 'Executor 3'] or (user_role == 'Cross-Checker' and context == 'validation_workflow'):
            # Executors and Cross-Checker validation workflow: Ensure path starts with E:\Work space (case insensitive)
            if not path.upper().startswith('E:\\WORK SPACE'):
                logger.warning(f"FILE_BROWSER | SECURITY | {user_role} attempted access outside E:\\Work space: {path}")
                path = ALLOWED_ROOT
        else:
            # Other roles: Ensure path starts with E:\ (case insensitive)
            if not path.upper().startswith('E:\\'):
                logger.warning(f"FILE_BROWSER | SECURITY | {user_role} attempted access outside E:\\ drive: {path}")
                path = ALLOWED_ROOT

        # Additional security check: resolve path and ensure it's still within allowed root
        try:
            resolved_path = os.path.abspath(path)
            if user_role in ['Executor 1', 'Executor 2', 'Executor 3'] or (user_role == 'Cross-Checker' and context == 'validation_workflow'):
                # Executors and Cross-Checker validation workflow: Must stay within E:\Work space
                if not resolved_path.upper().startswith('E:\\WORK SPACE'):
                    logger.warning(f"FILE_BROWSER | SECURITY | {user_role} resolved path outside E:\\Work space: {resolved_path}")
                    path = ALLOWED_ROOT
                else:
                    path = resolved_path
            else:
                # Other roles: Must stay within E:\ drive
                if not resolved_path.upper().startswith('E:\\'):
                    logger.warning(f"FILE_BROWSER | SECURITY | {user_role} resolved path outside E:\\ drive: {resolved_path}")
                    path = ALLOWED_ROOT
                else:
                    path = resolved_path
        except:
            path = ALLOWED_ROOT

        # Handle root requests based on role and context
        if user_role in ['Executor 1', 'Executor 2', 'Executor 3'] or (user_role == 'Cross-Checker' and context == 'validation_workflow'):
            # Executors and Cross-Checker validation workflow: If requesting E:\ or E:\Work space, start at E:\Work space
            if path.upper() in ['E:', 'E:\\', 'E:\\WORK SPACE']:
                path = ALLOWED_ROOT
        else:
            # Other roles: If requesting E:\ specifically, start at E:\
            if path.upper() in ['E:', 'E:\\']:
                path = ALLOWED_ROOT

        # Final security validation: Role-based path restrictions with context awareness
        if user_role in ['Executor 1', 'Executor 2', 'Executor 3'] or (user_role == 'Cross-Checker' and context == 'validation_workflow'):
            # Executors and Cross-Checker validation workflow: Must be within E:\Work space
            if not path.upper().startswith('E:\\WORK SPACE'):
                logger.error(f"FILE_BROWSER | SECURITY VIOLATION | {user_role} final check failed for path: {path}")
                return jsonify({'success': False, 'error': 'Access restricted to E:\\Work space only'})
        else:
            # Other roles: Must be within E:\ drive
            if not path.upper().startswith('E:\\'):
                logger.error(f"FILE_BROWSER | SECURITY VIOLATION | {user_role} final check failed for path: {path}")
                return jsonify({'success': False, 'error': 'Access restricted to E:\\ drive only'})

        # Validate and normalize path
        if not os.path.exists(path):
            return jsonify({'success': False, 'error': f'Path does not exist: {path}'})

        try:
            # Add parent directory navigation (role-based restrictions with context awareness)
            if user_role in ['Executor 1', 'Executor 2', 'Executor 3'] or (user_role == 'Cross-Checker' and context == 'validation_workflow'):
                # Executors and Cross-Checker validation workflow: Allow navigation up but not above E:\Work space
                if len(path) > len('E:\\Work space') and path.upper() != 'E:\\WORK SPACE':
                    parent = os.path.dirname(path)
                    if parent and parent != path and parent.upper().startswith('E:\\WORK SPACE'):
                        items.append({
                            'name': '.. (Up one level)',
                            'path': parent,
                            'type': 'folder',
                            'size': 0
                        })
            else:
                # Other roles: Allow navigation up but not above E:\
                if len(path) > 3 and path.upper() != 'E:\\':
                    parent = os.path.dirname(path)
                    if parent and parent != path and parent.upper().startswith('E:\\'):
                        items.append({
                            'name': '.. (Up one level)',
                            'path': parent,
                            'type': 'folder',
                            'size': 0
                        })
            # No option to go back to drives list - restricted access only

            # List directory contents
            for item in os.listdir(path):
                item_path = os.path.join(path, item)

                try:
                    if os.path.isdir(item_path):
                        items.append({
                            'name': item,
                            'path': item_path,
                            'type': 'folder',
                            'size': 0
                        })
                    elif os.path.isfile(item_path):
                        # Filter by file type if specified
                        if file_type == 'video':
                            if not item.lower().endswith(('.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv')):
                                continue

                        size = os.path.getsize(item_path)
                        items.append({
                            'name': item,
                            'path': item_path,
                            'type': 'file',
                            'size': size
                        })
                except (PermissionError, OSError) as e:
                    # Add inaccessible items with error indication
                    items.append({
                        'name': f"{item} (Access Denied)",
                        'path': item_path,
                        'type': 'folder' if os.path.isdir(item_path) else 'file',
                        'size': 0
                    })
                    continue

        except PermissionError:
            return jsonify({'success': False, 'error': 'Permission denied to access this directory'})
        except Exception as e:
            return jsonify({'success': False, 'error': f'Error reading directory: {str(e)}'})

        return jsonify({
            'success': True,
            'path': path,
            'items': items
        })

    except Exception as e:
        logger.error(f"FILE_BROWSER | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/lookup_project_date', methods=['POST'])
@login_required
@role_required(['Executor 1', 'Executor 2', 'Executor 3', 'Admin'])
def lookup_project_date():
    """Lookup project date from Google Sheets Artifacts sheet with fallback to local JSON"""
    try:
        data = request.get_json()
        project_name = data.get('project_name', '').strip()

        if not project_name:
            return jsonify({'success': False, 'error': 'Project name is required'})

        logger.info(f"PROJECT_DATE_LOOKUP | Starting lookup for project: {project_name}")

        # Use local CSV file to avoid Google Sheets 403 errors
        logger.info(f"PROJECT_DATE_LOOKUP | Using local CSV file for project date lookup")
        return lookup_from_csv_file(project_name)

    except Exception as e:
        logger.error(f"PROJECT_DATE_LOOKUP | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

def lookup_from_csv_file(project_name):
    """Lookup project date from local CSV file"""
    try:
        logger.info(f"PROJECT_DATE_LOOKUP | Using CSV file lookup for: {project_name}")

        # CSV file path
        csv_file_path = "SP 25 Backlogs - Artifacts.csv"

        if not os.path.exists(csv_file_path):
            logger.error(f"PROJECT_DATE_LOOKUP | CSV file not found: {csv_file_path}")
            return jsonify({
                'success': False,
                'error': f'CSV file not found: {csv_file_path}'
            })

        import csv

        # Read and search the CSV file
        project_date = None
        found_row = None
        total_rows = 0

        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            csv_reader = csv.DictReader(csvfile)

            for row_index, row in enumerate(csv_reader, start=2):  # Start from 2 (header is row 1)
                total_rows += 1

                # Get filename and date from CSV
                filename_in_csv = row.get('filename', '').strip()
                date_in_csv = row.get('date', '').strip()

                # Check for EXACT match (case insensitive)
                if filename_in_csv.lower() == project_name.lower():
                    project_date = date_in_csv
                    found_row = row_index
                    logger.info(f"PROJECT_DATE_LOOKUP | Found exact match at row {found_row}: {filename_in_csv} -> {project_date}")
                    break

        logger.info(f"PROJECT_DATE_LOOKUP | Searched {total_rows} rows in CSV file")

        if project_date:
            logger.info(f"PROJECT_DATE_LOOKUP | SUCCESS (CSV) | Project: {project_name} | Date: {project_date}")
            return jsonify({
                'success': True,
                'project_date': project_date,
                'project_name': project_name,
                'found_row': found_row,
                'source': 'local_csv',
                'total_rows_searched': total_rows
            })
        else:
            logger.warning(f"PROJECT_DATE_LOOKUP | No match found in CSV for project: {project_name}")
            return jsonify({
                'success': True,
                'project_date': None,
                'message': f'Project Name not found in CSV file. Searched {total_rows} records.',
                'source': 'local_csv',
                'total_rows_searched': total_rows
            })

    except Exception as e:
        logger.error(f"PROJECT_DATE_LOOKUP | CSV lookup error: {str(e)}")
        return jsonify({'success': False, 'error': f'CSV lookup failed: {str(e)}'})

@app.route('/api/auto_populate_audio_code', methods=['POST'])
@login_required
@role_required(['Executor 1', 'Executor 2', 'Executor 3', 'Admin'])
def auto_populate_audio_code():
    """Auto-populate audio code from Google Sheets"""
    try:
        logger.info(f"AUDIO_CODE_AUTO_POPULATE | Request from user: {session.get('username')}")

        # Google Sheets configuration
        AUDIO_CODE_SHEET_ID = "12vG_869ivHGQmPRcXib03ZqQ1KbErGwgKw9TG-txuQ4"
        AUDIO_CODE_SHEET_NAME = "Audio Code"

        # Initialize Google Sheets client
        import gspread
        from google.oauth2.service_account import Credentials
        import ssl
        import urllib3

        # Disable SSL warnings for development
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        ssl._create_default_https_context = ssl._create_unverified_context

        # Setup credentials (using existing credentials.json)
        SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
        SERVICE_ACCOUNT_FILE = 'credentials.json'

        if not os.path.exists(SERVICE_ACCOUNT_FILE):
            logger.error(f"AUDIO_CODE_AUTO_POPULATE | Service account file not found: {SERVICE_ACCOUNT_FILE}")
            return jsonify({
                'success': False,
                'error': 'Google Sheets service account file not found'
            })

        credentials = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        gc = gspread.authorize(credentials)

        # Open the Audio Code sheet
        try:
            spreadsheet = gc.open_by_key(AUDIO_CODE_SHEET_ID)
            worksheet = spreadsheet.worksheet(AUDIO_CODE_SHEET_NAME)
        except Exception as sheet_error:
            logger.error(f"AUDIO_CODE_AUTO_POPULATE | Failed to open sheet: {str(sheet_error)}")
            return jsonify({
                'success': False,
                'error': f'Failed to access Google Sheet: {str(sheet_error)}'
            })

        # Get all values from the sheet
        all_values = worksheet.get_all_values()

        if len(all_values) < 2:  # No data rows
            logger.error(f"AUDIO_CODE_AUTO_POPULATE | No data found in sheet")
            return jsonify({
                'success': False,
                'error': 'No audio codes found in the sheet'
            })

        # Find first empty status (Column B)
        audio_code = None
        found_row = None

        for row_index, row in enumerate(all_values[1:], start=2):  # Skip header, start from row 2
            if len(row) >= 2:  # Ensure we have both columns
                code = row[0].strip()  # Column A: Audio Code
                status = row[1].strip() if len(row) > 1 else ""  # Column B: Status

                # Check for first empty status
                if not status and code:  # Empty status and valid code
                    audio_code = code
                    found_row = row_index
                    break

        if not audio_code:
            logger.warning(f"AUDIO_CODE_AUTO_POPULATE | No available audio codes found")
            return jsonify({
                'success': False,
                'error': 'No available audio codes found. All codes are currently in use.'
            })

        # Update the status to "Blocked" in Google Sheets
        try:
            worksheet.update_cell(found_row, 2, 'Blocked')  # Column B (Status)
            logger.info(f"AUDIO_CODE_AUTO_POPULATE | Updated row {found_row} status to 'Blocked'")
        except Exception as update_error:
            logger.error(f"AUDIO_CODE_AUTO_POPULATE | Failed to update sheet: {str(update_error)}")
            return jsonify({
                'success': False,
                'error': f'Failed to update Google Sheet: {str(update_error)}'
            })

        logger.info(f"AUDIO_CODE_AUTO_POPULATE | SUCCESS | Code: {audio_code} | Row: {found_row} | Status: Blocked")

        return jsonify({
            'success': True,
            'audio_code': audio_code,
            'row_number': found_row,
            'status': 'Blocked',
            'source': 'google_sheets',
            'message': f'Audio code {audio_code} has been assigned and marked as Blocked'
        })

    except Exception as e:
        logger.error(f"AUDIO_CODE_AUTO_POPULATE | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': f'Audio code auto-populate failed: {str(e)}'})

@app.route('/api/release_audio_code', methods=['POST'])
@login_required
@role_required(['Cross-Checker', 'Admin'])
def release_audio_code():
    """Release audio code back to available pool using Google Sheets"""
    try:
        data = request.get_json()
        audio_code = data.get('audio_code', '').strip()

        if not audio_code:
            return jsonify({'success': False, 'error': 'Audio code is required'})

        logger.info(f"AUDIO_CODE_RELEASE | Request from user: {session.get('username')} | Code: {audio_code}")

        # Google Sheets configuration
        AUDIO_CODE_SHEET_ID = "12vG_869ivHGQmPRcXib03ZqQ1KbErGwgKw9TG-txuQ4"
        AUDIO_CODE_SHEET_NAME = "Audio Code"

        # Initialize Google Sheets client
        import gspread
        from google.oauth2.service_account import Credentials
        import ssl
        import urllib3

        # Disable SSL warnings for development
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        ssl._create_default_https_context = ssl._create_unverified_context

        # Setup credentials (using existing credentials.json)
        SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
        SERVICE_ACCOUNT_FILE = 'credentials.json'

        if not os.path.exists(SERVICE_ACCOUNT_FILE):
            logger.error(f"AUDIO_CODE_RELEASE | Service account file not found: {SERVICE_ACCOUNT_FILE}")
            return jsonify({
                'success': False,
                'error': 'Google Sheets service account file not found'
            })

        credentials = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)
        gc = gspread.authorize(credentials)

        # Open the Audio Code sheet
        try:
            spreadsheet = gc.open_by_key(AUDIO_CODE_SHEET_ID)
            worksheet = spreadsheet.worksheet(AUDIO_CODE_SHEET_NAME)
        except Exception as sheet_error:
            logger.error(f"AUDIO_CODE_RELEASE | Failed to open sheet: {str(sheet_error)}")
            return jsonify({
                'success': False,
                'error': f'Failed to access Google Sheet: {str(sheet_error)}'
            })

        # Get all values from the sheet
        all_values = worksheet.get_all_values()

        if len(all_values) < 2:  # No data rows
            logger.error(f"AUDIO_CODE_RELEASE | No data found in sheet")
            return jsonify({
                'success': False,
                'error': 'No audio codes found in the sheet'
            })

        # Find the audio code to release
        found_code = False
        found_row = None
        old_status = None

        for row_index, row in enumerate(all_values[1:], start=2):  # Skip header, start from row 2
            if len(row) >= 1:  # Ensure we have audio code column
                code = row[0].strip()  # Column A: Audio Code
                status = row[1].strip() if len(row) > 1 else ""  # Column B: Status

                # Check if this is the code we're looking for
                if code == audio_code:
                    found_code = True
                    found_row = row_index
                    old_status = status
                    break

        if not found_code:
            logger.warning(f"AUDIO_CODE_RELEASE | Audio code not found: {audio_code}")
            return jsonify({
                'success': False,
                'error': f'Audio code "{audio_code}" not found in the system'
            })

        # Update the status to "Code to be reused" in Google Sheets
        try:
            worksheet.update_cell(found_row, 2, 'Code to be reused')  # Column B (Status)
            logger.info(f"AUDIO_CODE_RELEASE | Updated row {found_row} status to 'Code to be reused'")
        except Exception as update_error:
            logger.error(f"AUDIO_CODE_RELEASE | Failed to update sheet: {str(update_error)}")
            return jsonify({
                'success': False,
                'error': f'Failed to update Google Sheet: {str(update_error)}'
            })

        logger.info(f"AUDIO_CODE_RELEASE | SUCCESS | Code: {audio_code} | Row: {found_row} | Old: '{old_status}' | New: 'Code to be reused'")

        return jsonify({
            'success': True,
            'audio_code': audio_code,
            'row_number': found_row,
            'old_status': old_status,
            'new_status': 'Code to be reused',
            'source': 'google_sheets',
            'message': f'Audio code {audio_code} has been released and marked as "Code to be reused"'
        })

    except Exception as e:
        logger.error(f"AUDIO_CODE_RELEASE | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': f'Audio code release failed: {str(e)}'})

def lookup_from_local_json(project_name):
    """Lookup project date from local JSON file"""
    try:
        logger.info(f"PROJECT_DATE_LOOKUP | Using local JSON fallback for: {project_name}")

        # Check if mock data file exists
        if not os.path.exists('mock_project_dates.json'):
            # Create default mock data
            mock_data = {
                "Promo_Himalayan-Sojourn": "14-Mar-2021",
                "Documentary_Wildlife_Africa": "25-Dec-2020",
                "Training_Module_Advanced": "15-Jan-2023",
                "Marketing_Campaign_Q4": "08-Sep-2022",
                "Product_Launch_2024": "22-Nov-2024",
                "Conference_Tech_Summit": "05-Apr-2023",
                "Tutorial_Series_Beginner": "18-Jul-2022",
                "Annual_Report_Financial": "31-Dec-2023",
                "Workshop_Leadership": "12-May-2024",
                "Webinar_Digital_Marketing": "03-Aug-2023"
            }

            with open('mock_project_dates.json', 'w') as f:
                json.dump(mock_data, f, indent=2)

            logger.info(f"PROJECT_DATE_LOOKUP | Created mock_project_dates.json with sample data")

        # Load and search the JSON data
        with open('mock_project_dates.json', 'r') as f:
            project_dates = json.load(f)

        # Search for exact match (case insensitive)
        project_date = None
        for filename, date in project_dates.items():
            if filename.lower() == project_name.lower():
                project_date = date
                break

        if project_date:
            logger.info(f"PROJECT_DATE_LOOKUP | SUCCESS (JSON) | Project: {project_name} | Date: {project_date}")
            return jsonify({
                'success': True,
                'project_date': project_date,
                'project_name': project_name,
                'source': 'local_json'
            })
        else:
            logger.warning(f"PROJECT_DATE_LOOKUP | No match found in JSON for project: {project_name}")
            return jsonify({
                'success': True,
                'project_date': None,
                'message': f'Project Name not found in local data.',
                'source': 'local_json'
            })

    except Exception as e:
        logger.error(f"PROJECT_DATE_LOOKUP | JSON fallback error: {str(e)}")
        return jsonify({'success': False, 'error': f'Local data lookup failed: {str(e)}'})

def lookup_from_google_sheets(project_name):
    """Lookup project date from Google Sheets"""
    logger.info(f"PROJECT_DATE_LOOKUP | Attempting Google Sheets lookup for: {project_name}")

    # Import Google Sheets libraries
    import gspread
    from google.oauth2.service_account import Credentials
    import ssl
    import urllib3
    import httplib2
    import certifi

    # COMPLETE SSL BYPASS - Disable all SSL verification
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # Disable SSL globally
    ssl._create_default_https_context = ssl._create_unverified_context

    # Set environment variables to bypass SSL completely
    os.environ['PYTHONHTTPSVERIFY'] = '0'
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    os.environ['SSL_VERIFY'] = 'False'

    # Set up credentials with complete SSL bypass
    SCOPES = [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive'
    ]
    logger.info(f"PROJECT_DATE_LOOKUP | Loading credentials with complete SSL bypass")

    if not os.path.exists('credentials.json'):
        logger.error(f"PROJECT_DATE_LOOKUP | credentials.json file not found")
        raise Exception('Google Sheets credentials not configured')

    # Create custom HTTP client with complete SSL bypass
    http = httplib2.Http(disable_ssl_certificate_validation=True)

    # Additional SSL bypass for httplib2
    http.ca_certs = None

    # Load credentials and authorize with complete SSL bypass
    try:
        creds = Credentials.from_service_account_file('credentials.json', scopes=SCOPES)

        # Create client with SSL bypass
        client = gspread.authorize(creds)

        # Override the client's HTTP with our SSL-disabled version
        client.http = http

        # Additional SSL bypass for the client session
        if hasattr(client, 'session'):
            client.session.verify = False

        logger.info(f"PROJECT_DATE_LOOKUP | Google Sheets client authorized with complete SSL bypass")

    except Exception as cred_error:
        logger.error(f"PROJECT_DATE_LOOKUP | Credentials error: {str(cred_error)}")
        raise Exception(f'Google Sheets authentication failed: {str(cred_error)}')

    # Access the Artifacts sheet with enhanced error handling
    try:
        spreadsheet = client.open_by_key(MAIN_SHEET_ID)
        logger.info(f"PROJECT_DATE_LOOKUP | Successfully opened spreadsheet: {spreadsheet.title}")

    except gspread.SpreadsheetNotFound:
        logger.error(f"PROJECT_DATE_LOOKUP | Spreadsheet not found or access denied")
        raise Exception('Google Sheet not found or insufficient permissions. Please share the sheet with the service account.')
    except Exception as sheet_error:
        logger.error(f"PROJECT_DATE_LOOKUP | Error accessing spreadsheet: {str(sheet_error)}")
        raise Exception(f'Error accessing Google Sheet: {str(sheet_error)}')

    # Get the Artifacts worksheet
    try:
        artifacts_sheet = spreadsheet.worksheet("Artifacts")
        logger.info(f"PROJECT_DATE_LOOKUP | Successfully accessed Artifacts sheet")

    except gspread.WorksheetNotFound:
        logger.error(f"PROJECT_DATE_LOOKUP | Artifacts sheet not found")

        # Try to create the Artifacts sheet
        try:
            logger.info(f"PROJECT_DATE_LOOKUP | Creating new Artifacts sheet...")
            artifacts_sheet = spreadsheet.add_worksheet(title="Artifacts", rows="1000", cols="10")

            # Add headers
            headers = ["Filename", "Project Date"]
            artifacts_sheet.update('A1:B1', [headers])

            # Add sample data
            sample_data = [
                ["Promo_Himalayan-Sojourn", "14-Mar-2021"],
                ["Documentary_Wildlife_Africa", "25-Dec-2020"],
                ["Training_Module_Advanced", "15-Jan-2023"]
            ]

            for i, row_data in enumerate(sample_data, start=2):
                artifacts_sheet.update(f'A{i}:B{i}', [row_data])

            logger.info(f"PROJECT_DATE_LOOKUP | Created and populated Artifacts sheet")

        except Exception as create_error:
            logger.error(f"PROJECT_DATE_LOOKUP | Failed to create Artifacts sheet: {str(create_error)}")
            raise Exception('Artifacts sheet not found and could not be created. Check permissions.')

    except Exception as ws_error:
        logger.error(f"PROJECT_DATE_LOOKUP | Error accessing Artifacts sheet: {str(ws_error)}")
        raise Exception(f'Error accessing Artifacts sheet: {str(ws_error)}')

    # Get all values from the sheet (starting from row 2, skipping header)
    try:
        all_values = artifacts_sheet.get_all_values()
        logger.info(f"PROJECT_DATE_LOOKUP | Retrieved {len(all_values)} rows from Artifacts sheet")

        if len(all_values) < 2:
            logger.warning(f"PROJECT_DATE_LOOKUP | No data rows found in Artifacts sheet")
            raise Exception('No data found in Artifacts sheet')

        # Search for the project name in Column A (index 0) - EXACT MATCH ONLY
        project_date = None
        found_row = None

        for row_index, row in enumerate(all_values[1:], start=2):  # Skip header row
            if len(row) >= 2:  # Ensure row has at least 2 columns
                filename_in_sheet = row[0].strip()  # Column A
                date_in_sheet = row[1].strip()      # Column B

                # Check for EXACT match only (as per requirements)
                if filename_in_sheet.lower() == project_name.lower():
                    project_date = date_in_sheet
                    found_row = row_index
                    logger.info(f"PROJECT_DATE_LOOKUP | Found exact match at row {found_row}: {filename_in_sheet} -> {project_date}")
                    break

        if project_date:
            logger.info(f"PROJECT_DATE_LOOKUP | SUCCESS | Project: {project_name} | Date: {project_date}")
            return jsonify({
                'success': True,
                'project_date': project_date,
                'project_name': project_name,
                'found_row': found_row,
                'source': 'google_sheets'
            })
        else:
            logger.warning(f"PROJECT_DATE_LOOKUP | No match found for project: {project_name}")
            return jsonify({
                'success': True,
                'project_date': None,
                'message': f'Project Name not found in Artifacts sheet.',
                'source': 'google_sheets'
            })

    except Exception as e:
        logger.error(f"PROJECT_DATE_LOOKUP | Error reading Artifacts sheet: {str(e)}")
        raise Exception(f'Error reading Artifacts sheet: {str(e)}')

    except Exception as e:
        logger.error(f"PROJECT_DATE_LOOKUP | Google Sheets error: {str(e)}")
        raise Exception(f'Google Sheets lookup failed: {str(e)}')

@app.route('/api/validate_folder/<int:processing_id>', methods=['POST'])
@login_required
@role_required(['Cross-Checker'])
@log_action("CROSS_CHECK_VALIDATION")
def validate_folder(processing_id):
    """Complete validation workflow with Google Sheets, audio extraction, and folder management"""
    try:
        data = request.get_json()
        action = data.get('action')  # 'approve' or 'reject'
        notes = data.get('notes', '')
        editing_required = data.get('editing_required', False)
        mov_file_path = data.get('mov_file_path', '')
        # Use fixed audio extraction path instead of user input
        audio_output_path = 'E:\\Extracted Audios'

        # Get destination folder path (from radio button selection)
        final_folder_path = data.get('final_folder_path', '')

        logger.info(f"VALIDATION | Received final_folder_path: '{final_folder_path}'")
        logger.info(f"VALIDATION | Full request data: {data}")

        # Validate destination folder path (should be one of the fixed options)
        valid_destinations = [
            'E:\\To Be Deleted',
            'E:\\To Be Ingested',
            'E:\\For Editing'
        ]

        if not final_folder_path:
            logger.error(f"VALIDATION | No destination path provided")
            return jsonify({'success': False, 'error': 'No destination path provided. Please select a destination folder.'})

        if final_folder_path not in valid_destinations:
            logger.error(f"VALIDATION | Invalid destination path: '{final_folder_path}' | Valid options: {valid_destinations}")
            return jsonify({'success': False, 'error': f'Invalid destination path: "{final_folder_path}". Must be one of: {", ".join(valid_destinations)}'})

        updated_metadata = data.get('updated_metadata', {})

        # Ensure both audio extraction and destination directories exist
        directories_to_create = [audio_output_path, final_folder_path]

        for directory in directories_to_create:
            try:
                os.makedirs(directory, exist_ok=True)
                logger.info(f"VALIDATION | Directory ensured: {directory}")
            except Exception as e:
                logger.error(f"VALIDATION | Failed to create directory {directory}: {e}")
                return jsonify({'success': False, 'error': f'Failed to create directory {directory}: {str(e)}'})

        logger.info(f"VALIDATION | Using fixed audio extraction path: {audio_output_path}")
        logger.info(f"VALIDATION | Using selected destination path: {final_folder_path}")

        if action != 'approve':
            # Simple rejection
            conn = sqlite3.connect(DATABASE_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE verification_queue
                SET status = 'rejected', cross_checker_id = ?, verified_at = CURRENT_TIMESTAMP, notes = ?
                WHERE processing_id = ?
            ''', (session.get('user_id'), notes, processing_id))

            cursor.execute('UPDATE processing_queue SET status = "rejected" WHERE id = ?', (processing_id,))

            # Log action
            log_system_action(cursor, session.get('user_id'), 'FOLDER_REJECTED',
                            f'Processing ID: {processing_id}, Notes: {notes}')

            conn.commit()
            conn.close()

            return jsonify({'success': True, 'message': 'Folder rejected successfully'})

        # Approval workflow
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get processing data
        cursor.execute('''
            SELECT pq.metadata, pq.folder_name, pq.folder_path, pq.executor_id, pq.created_at, u.username
            FROM processing_queue pq
            JOIN users u ON pq.executor_id = u.id
            WHERE pq.id = ?
        ''', (processing_id,))

        result = cursor.fetchone()
        if not result:
            return jsonify({'success': False, 'error': 'Processing record not found'})

        metadata_json, folder_name, folder_path, executor_id, created_at, executor_username = result
        metadata = json.loads(metadata_json)

        # Use updated metadata from Cross-Checker if provided
        if updated_metadata:
            logger.info(f"VALIDATION | Updating metadata with Cross-Checker changes: {updated_metadata}")
            metadata.update(updated_metadata)
            # Update the metadata in the database
            cursor.execute('UPDATE processing_queue SET metadata = ? WHERE id = ?',
                          (json.dumps(metadata), processing_id))
            # Commit the metadata update immediately to ensure it's saved
            conn.commit()
            logger.info(f"VALIDATION | Metadata updated in database with new values")

        # Step 1: Upload to Google Sheets
        sheet_success = upload_to_google_sheets(metadata, executor_username, created_at,
                                               session.get('username'), editing_required)

        # Step 2: Extract audio if MOV file provided
        audio_success = False
        audio_file_path = ''
        if mov_file_path and audio_output_path:
            # Generate proper audio filename using updated metadata
            audio_code = metadata.get('audio_code', 'UNKNOWN')
            soft_renamed = metadata.get('soft_renamed', folder_name)

            # Create the exact filename format: AudioCode_SoftRenamedName.wav
            audio_filename = f"{audio_code}_{soft_renamed}.wav"

            # Full path for the output audio file
            audio_output_full_path = os.path.join(audio_output_path, audio_filename)

            logger.info(f"VALIDATION | Audio extraction with updated metadata:")
            logger.info(f"VALIDATION | Audio Code: {audio_code}")
            logger.info(f"VALIDATION | Soft Renamed: {soft_renamed}")
            logger.info(f"VALIDATION | Audio Filename: {audio_filename}")

            audio_success, audio_file_path = extract_audio_ffmpeg(mov_file_path, audio_output_full_path)

        # Step 3: Rename and move folder
        folder_success = False
        new_folder_path = ''
        if final_folder_path and metadata.get('soft_renamed'):
            soft_renamed = metadata.get('soft_renamed')
            logger.info(f"VALIDATION | Folder rename/move with updated metadata:")
            logger.info(f"VALIDATION | Original folder name: {folder_name}")
            logger.info(f"VALIDATION | Soft renamed (updated): {soft_renamed}")
            logger.info(f"VALIDATION | Source path: {folder_path}")
            logger.info(f"VALIDATION | Destination: {final_folder_path}")

            folder_success, new_folder_path = rename_and_move_folder(folder_path,
                                                                   soft_renamed,
                                                                   final_folder_path)

        # Update verification queue
        cursor.execute('''
            UPDATE verification_queue
            SET status = 'verified', cross_checker_id = ?, verified_at = CURRENT_TIMESTAMP,
                notes = ?, editing_required = ?, audio_extracted = ?, folder_renamed = ?,
                google_sheet_updated = ?, audio_file_path = ?, final_folder_path = ?
            WHERE processing_id = ?
        ''', (session.get('user_id'), notes, editing_required, audio_success, folder_success,
              sheet_success, audio_file_path, new_folder_path, processing_id))

        # Update processing queue
        cursor.execute('UPDATE processing_queue SET status = "verified" WHERE id = ?', (processing_id,))

        # Step 4: Add to editing queue if required
        if editing_required:
            cursor.execute('''
                INSERT INTO editing_queue (verification_id, status)
                VALUES ((SELECT id FROM verification_queue WHERE processing_id = ?), 'pending')
            ''', (processing_id,))

        # Log all actions
        log_system_action(cursor, session.get('user_id'), 'FOLDER_VALIDATED',
                        f'Processing ID: {processing_id}, Google Sheets: {sheet_success}, '
                        f'Audio: {audio_success}, Folder: {folder_success}, Editing: {editing_required}')

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'Folder validated successfully',
            'details': {
                'google_sheets': sheet_success,
                'audio_extraction': audio_success,
                'folder_management': folder_success,
                'editing_queued': editing_required
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Helper functions
def convert_iso_date(iso_date):
    """Convert ISO date format to readable format"""
    try:
        if not iso_date:
            return ''

        # Parse ISO format like 2025-06-30T06:42:24Z
        if 'T' in iso_date:
            date_part = iso_date.split('T')[0]
            date_obj = datetime.strptime(date_part, '%Y-%m-%d')
            return date_obj.strftime('%d-%B-%Y')

        return iso_date
    except:
        return iso_date

def convert_duration(duration):
    """Convert duration format from 0:01:54 to 01Min-54Secs"""
    try:
        if not duration or ':' not in duration:
            return duration

        parts = duration.split(':')
        if len(parts) == 3:
            hours, minutes, seconds = parts
            if int(hours) > 0:
                return f"{int(hours)}Hr-{int(minutes)}Min-{int(seconds)}Secs"
            else:
                return f"{int(minutes)}Min-{int(seconds)}Secs"
        elif len(parts) == 2:
            minutes, seconds = parts
            return f"{int(minutes)}Min-{int(seconds)}Secs"

        return duration
    except:
        return duration

def get_platform_name(sheet_name):
    """Get platform name based on sheet name"""
    platform_mapping = {
        'SG': 'Sadhguru YouTube',
        'IF': 'Isha Foundation YouTube',
        'IG': 'Sadhguru Instagram'
    }
    return platform_mapping.get(sheet_name, sheet_name)

def convert_utc_to_local(utc_timestamp_str):
    """Convert UTC timestamp string to local time"""
    try:
        if not utc_timestamp_str:
            return ''

        # Parse the UTC timestamp
        utc_dt = datetime.strptime(utc_timestamp_str, '%Y-%m-%d %H:%M:%S')

        # Set timezone to UTC
        utc_dt = pytz.UTC.localize(utc_dt)

        # Convert to local timezone (you can change this to your specific timezone)
        # For now, using system local timezone
        local_tz = pytz.timezone('Asia/Kolkata')  # Change this to your timezone
        local_dt = utc_dt.astimezone(local_tz)

        # Return formatted local time
        return local_dt.strftime('%Y-%m-%d %H:%M:%S')
    except:
        # If conversion fails, return original
        return utc_timestamp_str

def log_system_action(cursor, user_id, action, details):
    """Log system actions to database"""
    cursor.execute('''
        INSERT INTO system_logs (user_id, action, details)
        VALUES (?, ?, ?)
    ''', (user_id, action, details))

def upload_to_google_sheets(metadata, executor_username, executor_timestamp, crosschecker_username, editing_required):
    """Upload validated metadata to Google Sheets with comprehensive 30+ fields"""
    try:
        logger.info(f"GOOGLE_SHEETS_UPLOAD | Starting upload process...")
        logger.info(f"GOOGLE_SHEETS_UPLOAD | Sheet ID: {MAIN_SHEET_ID}")
        logger.info(f"GOOGLE_SHEETS_UPLOAD | Sheet Name: {MAIN_SHEET_NAME}")

        client = init_google_sheets()
        if not client:
            logger.error(f"GOOGLE_SHEETS_UPLOAD | Failed to initialize Google Sheets client")
            return False

        logger.info(f"GOOGLE_SHEETS_UPLOAD | Client initialized successfully")

        spreadsheet = client.open_by_key(MAIN_SHEET_ID)
        logger.info(f"GOOGLE_SHEETS_UPLOAD | Spreadsheet opened: {spreadsheet.title}")

        worksheet = spreadsheet.worksheet(MAIN_SHEET_NAME)
        logger.info(f"GOOGLE_SHEETS_UPLOAD | Worksheet accessed: {worksheet.title}")

        # Prepare comprehensive row data matching the new header structure
        row_data = [
            # Core Identification
            metadata.get('ocd_number', ''),
            metadata.get('audio_code', ''),
            metadata.get('video_id', ''),
            metadata.get('title', ''),
            metadata.get('description', ''),

            # Content Classification
            metadata.get('video_type', ''),
            'Social Media' if metadata.get('distribution_type') == 'social' else 'Internal',
            metadata.get('component', ''),
            metadata.get('language', ''),
            metadata.get('platform', ''),

            # Dates & Duration
            metadata.get('project_date', ''),
            metadata.get('publish_date', '') or metadata.get('project_date', ''),
            metadata.get('project_name', ''),  # NEW: Project Name field
            metadata.get('duration', ''),
            metadata.get('total_duration', ''),

            # URLs & Files
            metadata.get('url', ''),
            metadata.get('transcription_file', ''),
            metadata.get('transcription_status', ''),  # NEW: Transcription Status field
            metadata.get('backup_type', ''),
            metadata.get('soft_renamed', ''),

            # Content Details
            ', '.join(metadata.get('content_tags', [])) if isinstance(metadata.get('content_tags'), list) else metadata.get('content_tags', ''),
            metadata.get('additional_notes', ''),
            metadata.get('keywords', ''),
            metadata.get('category', ''),
            metadata.get('subcategory', ''),

            # Technical Details
            metadata.get('resolution', '1920x1080'),
            metadata.get('format', 'MP4'),
            metadata.get('file_size', ''),
            metadata.get('audio_quality', '320 kbps'),
            metadata.get('video_quality', '1080p HD'),

            # Workflow Tracking
            executor_username,
            executor_timestamp,
            crosschecker_username,
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'Approved',  # Validation Status
            'Yes' if editing_required else 'No',
            '',  # Editor Name - to be updated later
            '',  # Editor Timestamp - to be updated later
            'Processed' if not editing_required else 'In Editing',  # Final Status

            # New Fields: With Logo & Influencer
            metadata.get('with_logo', ''),
            metadata.get('influencer', ''),

            # System Metadata
            '',  # Processing ID - to be updated
            metadata.get('folder_path', ''),
            '',  # Final Folder Path - to be updated later
            datetime.now().strftime("%Y-%m-%d"),
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            f"Processed by {executor_username}, validated by {crosschecker_username}"
        ]

        # Append row to sheet
        logger.info(f"GOOGLE_SHEETS_UPLOAD | Appending row with {len(row_data)} columns")
        logger.info(f"GOOGLE_SHEETS_UPLOAD | Row data preview: Title='{metadata.get('title', 'N/A')}', Audio Code='{metadata.get('audio_code', 'N/A')}'")

        worksheet.append_row(row_data)
        logger.info(f"GOOGLE_SHEETS_UPLOAD | Row appended successfully")

        logger.info(f"GOOGLE_SHEETS_UPLOAD | SUCCESS | Title: {metadata.get('title', 'N/A')} | Executor: {executor_username} | Cross-Checker: {crosschecker_username}")

        return True

    except Exception as e:
        logger.error(f"GOOGLE_SHEETS_UPLOAD | ERROR | {str(e)}")
        logger.error(f"GOOGLE_SHEETS_UPLOAD | ERROR TYPE | {type(e).__name__}")
        logger.error(f"GOOGLE_SHEETS_UPLOAD | SHEET_ID | {MAIN_SHEET_ID}")
        logger.error(f"GOOGLE_SHEETS_UPLOAD | SHEET_NAME | {MAIN_SHEET_NAME}")
        print(f"Google Sheets upload error: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

def extract_audio_ffmpeg(mov_file_path, output_path):
    """Extract audio from MOV file using FFMPEG"""
    start_time = datetime.now()

    try:
        logger.info(f"AUDIO_EXTRACTION | Starting FFmpeg extraction | Input: {mov_file_path} | Output: {output_path}")

        if not os.path.exists(mov_file_path):
            error_msg = f"MOV file not found: {mov_file_path}"
            logger.error(f"AUDIO_EXTRACTION | {error_msg}")
            return False, ''

        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # FFmpeg path - try multiple locations
        ffmpeg_paths = [
            'ffmpeg/ffmpeg.exe',  # Local installation
            'ffmpeg.exe',
            'ffmpeg',
            'C:/ffmpeg/bin/ffmpeg.exe'
        ]

        ffmpeg_cmd = None
        for path in ffmpeg_paths:
            if os.path.exists(path) or path in ['ffmpeg.exe', 'ffmpeg']:
                ffmpeg_cmd = path
                break

        if not ffmpeg_cmd:
            print("FFmpeg not found in any location")
            return False, ''

        # FFMPEG command for audio extraction
        cmd = [
            ffmpeg_cmd, '-i', mov_file_path,
            '-vn',  # No video
            '-acodec', 'pcm_s16le',  # Audio codec
            '-ar', '44100',  # Sample rate
            '-ac', '2',  # Audio channels
            '-y',  # Overwrite output file
            output_path
        ]

        logger.info(f"AUDIO_EXTRACTION | Executing FFmpeg: {' '.join(cmd)}")

        # Execute FFMPEG with timeout
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        extraction_time = (datetime.now() - start_time).total_seconds()

        if result.returncode == 0 and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            logger.info(f"AUDIO_EXTRACTION | SUCCESS | Output: {output_path} | Size: {file_size} bytes | Duration: {extraction_time:.2f}s")
            return True, output_path
        else:
            logger.error(f"AUDIO_EXTRACTION | FAILED | Return code: {result.returncode} | Error: {result.stderr} | Duration: {extraction_time:.2f}s")
            return False, ''

    except subprocess.TimeoutExpired:
        extraction_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"AUDIO_EXTRACTION | TIMEOUT | Duration: {extraction_time:.2f}s")
        return False, ''
    except Exception as e:
        extraction_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"AUDIO_EXTRACTION | ERROR | {str(e)} | Duration: {extraction_time:.2f}s")
        return False, ''

def rename_and_move_folder(source_path, new_name, destination_base_path):
    """Rename folder and move to destination"""
    try:
        logger.info(f"FOLDER_RENAME_MOVE | Starting | Source: {source_path} | New Name: {new_name} | Destination: {destination_base_path}")

        if not os.path.exists(source_path):
            logger.error(f"FOLDER_RENAME_MOVE | FAILED | Source path does not exist: {source_path}")
            return False, ''

        # Create destination directory if it doesn't exist
        os.makedirs(destination_base_path, exist_ok=True)

        # New folder path
        new_folder_path = os.path.join(destination_base_path, new_name)

        # Move and rename
        shutil.move(source_path, new_folder_path)

        if os.path.exists(new_folder_path):
            logger.info(f"FOLDER_RENAME_MOVE | SUCCESS | New Path: {new_folder_path}")
            return True, new_folder_path
        else:
            return False, ''

    except Exception as e:
        print(f"Folder rename/move error: {e}")
        return False, ''

# Editor API Routes
@app.route('/api/start_editing/<int:editing_id>', methods=['POST'])
@login_required
@role_required(['Editor'])
def start_editing(editing_id):
    """Start editing an item"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE editing_queue
            SET status = 'in_progress', editor_id = ?, started_at = CURRENT_TIMESTAMP
            WHERE id = ? AND status = 'pending'
        ''', (session.get('user_id'), editing_id))

        if cursor.rowcount > 0:
            log_system_action(cursor, session.get('user_id'), 'EDITING_STARTED', f'Editing ID: {editing_id}')
            conn.commit()
            conn.close()
            return jsonify({'success': True, 'message': 'Editing started successfully'})
        else:
            conn.close()
            return jsonify({'success': False, 'error': 'Item not available for editing'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/complete_editing/<int:editing_id>', methods=['POST'])
@login_required
@role_required(['Editor'])
@log_action("COMPLETE_EDITING")
def complete_editing(editing_id):
    """Complete editing with folder movement and admin approval request"""
    try:
        data = request.get_json()
        source_folder = data.get('source_folder', '').strip()
        destination_folder = data.get('destination_folder', '').strip()
        editing_notes = data.get('editing_notes', '').strip()
        admin_review_notes = data.get('admin_review_notes', '').strip()
        request_admin_approval = data.get('request_admin_approval', False)

        logger.info(f"COMPLETE_EDITING | Starting completion for editing ID: {editing_id}")
        logger.info(f"COMPLETE_EDITING | Source: {source_folder}")
        logger.info(f"COMPLETE_EDITING | Destination: {destination_folder}")
        logger.info(f"COMPLETE_EDITING | Admin approval: {request_admin_approval}")

        # Validate required fields
        if not source_folder:
            return jsonify({'success': False, 'error': 'Source folder is required'})

        if not destination_folder:
            return jsonify({'success': False, 'error': 'Destination folder is required'})

        if not editing_notes:
            return jsonify({'success': False, 'error': 'Editing notes are required'})

        # Validate folder paths
        if not os.path.exists(source_folder):
            return jsonify({'success': False, 'error': f'Source folder does not exist: {source_folder}'})

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get editing item details
        cursor.execute('''
            SELECT eq.verification_id, vq.final_folder_path, pq.metadata, pq.folder_name
            FROM editing_queue eq
            JOIN verification_queue vq ON eq.verification_id = vq.id
            JOIN processing_queue pq ON vq.processing_id = pq.id
            WHERE eq.id = ? AND eq.editor_id = ?
        ''', (editing_id, session.get('user_id')))

        result = cursor.fetchone()
        if not result:
            conn.close()
            return jsonify({'success': False, 'error': 'Editing item not found or not assigned to you'})

        verification_id, current_folder_path, metadata_json, folder_name = result
        metadata = json.loads(metadata_json)

        # Move folder from source to destination
        folder_moved = False
        final_path = ''

        try:
            # Create destination directory if it doesn't exist
            os.makedirs(destination_folder, exist_ok=True)

            # Get folder name from source path
            source_folder_name = os.path.basename(source_folder.rstrip(os.sep))
            final_path = os.path.join(destination_folder, source_folder_name)

            # Move the folder
            if os.path.exists(source_folder):
                if os.path.exists(final_path):
                    # If destination exists, create a unique name
                    counter = 1
                    while os.path.exists(f"{final_path}_{counter}"):
                        counter += 1
                    final_path = f"{final_path}_{counter}"

                shutil.move(source_folder, final_path)
                folder_moved = True
                logger.info(f"COMPLETE_EDITING | Folder moved successfully: {source_folder} -> {final_path}")
            else:
                logger.warning(f"COMPLETE_EDITING | Source folder not found: {source_folder}")

        except Exception as move_error:
            logger.error(f"COMPLETE_EDITING | Error moving folder: {str(move_error)}")
            conn.close()
            return jsonify({'success': False, 'error': f'Error moving folder: {str(move_error)}'})

        # Update editing queue
        cursor.execute('''
            UPDATE editing_queue
            SET status = 'completed',
                completed_at = CURRENT_TIMESTAMP,
                notes = ?,
                final_editor_path = ?
            WHERE id = ? AND editor_id = ?
        ''', (editing_notes, final_path, editing_id, session.get('user_id')))

        # Create admin approval request if requested
        admin_approval_requested = False
        if request_admin_approval:
            try:
                cursor.execute('''
                    INSERT INTO admin_approval_requests
                    (editing_id, editor_id, request_type, request_notes, admin_review_notes,
                     source_folder, destination_folder, created_at, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 'pending')
                ''', (editing_id, session.get('user_id'), 'editing_completion',
                      editing_notes, admin_review_notes, source_folder, final_path))

                admin_approval_requested = True
                logger.info(f"COMPLETE_EDITING | Admin approval request created for editing ID: {editing_id}")

            except sqlite3.Error as e:
                # If admin_approval_requests table doesn't exist, create it
                if "no such table" in str(e).lower():
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS admin_approval_requests (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            editing_id INTEGER,
                            editor_id INTEGER,
                            request_type TEXT,
                            request_notes TEXT,
                            admin_review_notes TEXT,
                            source_folder TEXT,
                            destination_folder TEXT,
                            created_at TIMESTAMP,
                            reviewed_at TIMESTAMP,
                            reviewed_by INTEGER,
                            status TEXT DEFAULT 'pending',
                            admin_comments TEXT
                        )
                    ''')

                    # Try inserting again
                    cursor.execute('''
                        INSERT INTO admin_approval_requests
                        (editing_id, editor_id, request_type, request_notes, admin_review_notes,
                         source_folder, destination_folder, created_at, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 'pending')
                    ''', (editing_id, session.get('user_id'), 'editing_completion',
                          editing_notes, admin_review_notes, source_folder, final_path))

                    admin_approval_requested = True
                    logger.info(f"COMPLETE_EDITING | Admin approval request created (table created) for editing ID: {editing_id}")

        # Log the completion
        log_system_action(cursor, session.get('user_id'), 'EDITING_COMPLETED',
                         f'Editing ID: {editing_id}, Folder moved: {folder_moved}, Admin approval: {admin_approval_requested}')

        conn.commit()
        conn.close()

        logger.info(f"COMPLETE_EDITING | Successfully completed editing ID: {editing_id}")

        return jsonify({
            'success': True,
            'message': 'Editing completed successfully',
            'folder_moved': folder_moved,
            'final_path': final_path,
            'admin_approval_requested': admin_approval_requested
        })

    except Exception as e:
        logger.error(f"COMPLETE_EDITING | Error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

def move_to_final_editor_path(current_path, folder_name, final_editor_path):
    """Move folder to final editor path"""
    try:
        if not os.path.exists(current_path):
            return False, ''

        # Create destination directory if it doesn't exist
        os.makedirs(final_editor_path, exist_ok=True)

        # New folder path
        new_folder_path = os.path.join(final_editor_path, folder_name)

        # Move folder
        shutil.move(current_path, new_folder_path)

        if os.path.exists(new_folder_path):
            return True, new_folder_path
        else:
            return False, ''

    except Exception as e:
        print(f"Final editor move error: {e}")
        return False, ''

# Admin API Routes
@app.route('/api/admin/users', methods=['GET'])
@login_required
@role_required(['Admin'])
def get_all_users():
    """Get all users for admin management"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT id, username, role, created_at FROM users ORDER BY role, username')
        users = cursor.fetchall()

        conn.close()

        return jsonify({
            'success': True,
            'users': [{'id': u[0], 'username': u[1], 'role': u[2], 'created_at': u[3]} for u in users]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/users', methods=['POST'])
@login_required
@role_required(['Admin'])
def create_user():
    """Create new user"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        role = data.get('role', '').strip()

        if not username or not password or not role:
            return jsonify({'success': False, 'error': 'All fields are required'})

        # Check if username already exists
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'error': 'Username already exists'})

        # Create user
        hashed_password = generate_password_hash(password)
        cursor.execute('''
            INSERT INTO users (username, password_hash, role)
            VALUES (?, ?, ?)
        ''', (username, hashed_password, role))

        # Log action
        log_system_action(cursor, session.get('user_id'), 'USER_CREATED',
                        f'Created user: {username} with role: {role}')

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'User created successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/users/<int:user_id>', methods=['PUT'])
@login_required
@role_required(['Admin'])
def update_user(user_id):
    """Update user details"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        role = data.get('role', '').strip()
        new_password = data.get('password', '').strip()

        if not username or not role:
            return jsonify({'success': False, 'error': 'Username and role are required'})

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Check if user exists
        cursor.execute('SELECT username FROM users WHERE id = ?', (user_id,))
        result = cursor.fetchone()
        if not result:
            conn.close()
            return jsonify({'success': False, 'error': 'User not found'})

        old_username = result[0]

        # Update user
        if new_password:
            hashed_password = generate_password_hash(new_password)
            cursor.execute('''
                UPDATE users SET username = ?, role = ?, password_hash = ?
                WHERE id = ?
            ''', (username, role, hashed_password, user_id))
        else:
            cursor.execute('''
                UPDATE users SET username = ?, role = ?
                WHERE id = ?
            ''', (username, role, user_id))

        # Log action
        log_system_action(cursor, session.get('user_id'), 'USER_UPDATED',
                        f'Updated user: {old_username} → {username}, role: {role}')

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'User updated successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
@login_required
@role_required(['Admin'])
def delete_user(user_id):
    """Delete user"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get username before deletion
        cursor.execute('SELECT username FROM users WHERE id = ?', (user_id,))
        result = cursor.fetchone()
        if not result:
            conn.close()
            return jsonify({'success': False, 'error': 'User not found'})

        username = result[0]

        # Don't allow deletion of current admin
        if user_id == session.get('user_id'):
            conn.close()
            return jsonify({'success': False, 'error': 'Cannot delete your own account'})

        # Delete user
        cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))

        # Log action
        log_system_action(cursor, session.get('user_id'), 'USER_DELETED',
                        f'Deleted user: {username}')

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'User deleted successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/metadata_records')
@login_required
@role_required(['Admin'])
def get_metadata_records():
    """Get all metadata records for admin view"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT pq.id, pq.folder_name, pq.metadata, pq.status, pq.created_at,
                   u_exec.username as executor,
                   vq.status as verification_status, vq.verified_at,
                   u_cross.username as crosschecker,
                   eq.status as editing_status, eq.completed_at,
                   u_editor.username as editor
            FROM processing_queue pq
            LEFT JOIN users u_exec ON pq.executor_id = u_exec.id
            LEFT JOIN verification_queue vq ON pq.id = vq.processing_id
            LEFT JOIN users u_cross ON vq.cross_checker_id = u_cross.id
            LEFT JOIN editing_queue eq ON vq.id = eq.verification_id
            LEFT JOIN users u_editor ON eq.editor_id = u_editor.id
            ORDER BY pq.created_at DESC
        ''')

        records = cursor.fetchall()
        conn.close()

        # Format records for display
        formatted_records = []
        for record in records:
            metadata = json.loads(record[2]) if record[2] else {}
            formatted_records.append({
                'id': record[0],
                'folder_name': record[1],
                'metadata': metadata,
                'status': record[3],
                'created_at': convert_utc_to_local(record[4]),  # Convert to local time
                'executor': record[5],
                'verification_status': record[6],
                'verified_at': convert_utc_to_local(record[7]) if record[7] else None,  # Convert to local time
                'crosschecker': record[8],
                'editing_status': record[9],
                'completed_at': convert_utc_to_local(record[10]) if record[10] else None,  # Convert to local time
                'editor': record[11]
            })

        return jsonify({'success': True, 'records': formatted_records})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/system_logs')
@login_required
@role_required(['Admin'])
def get_system_logs():
    """Get system logs for admin view"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT sl.id, sl.action, sl.details, sl.timestamp, u.username
            FROM system_logs sl
            JOIN users u ON sl.user_id = u.id
            ORDER BY sl.timestamp DESC
            LIMIT 100
        ''')

        logs = cursor.fetchall()
        conn.close()

        formatted_logs = []
        for log in logs:
            formatted_logs.append({
                'id': log[0],
                'action': log[1],
                'details': log[2],
                'timestamp': log[3],
                'username': log[4]
            })

        return jsonify({'success': True, 'logs': formatted_logs})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/file_logs')
@login_required
@role_required(['Admin'])
def get_file_logs():
    """Get recent logs from log file for admin view"""
    try:
        log_file_path = 'logs/system.log'

        if not os.path.exists(log_file_path):
            return jsonify({'success': True, 'logs': [], 'message': 'No log file found'})

        # Read last 100 lines from log file
        with open(log_file_path, 'r') as f:
            lines = f.readlines()

        # Get last 100 lines
        recent_lines = lines[-100:] if len(lines) > 100 else lines

        # Parse log lines
        parsed_logs = []
        for line in recent_lines:
            line = line.strip()
            if line:
                parsed_logs.append({
                    'timestamp': line[:19] if len(line) > 19 else '',
                    'level': line[21:26].strip('[]') if len(line) > 26 else '',
                    'message': line[27:] if len(line) > 27 else line
                })

        return jsonify({'success': True, 'logs': parsed_logs})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/setup_google_sheets', methods=['POST'])
@login_required
@role_required(['Admin'])
def setup_google_sheets():
    """Setup Google Sheets headers and create test data"""
    try:
        logger.info("GOOGLE_SHEETS_SETUP | Starting comprehensive setup")

        # Step 1: Setup Google Sheets headers
        client = init_google_sheets()
        if not client:
            return jsonify({'success': False, 'error': 'Failed to connect to Google Sheets'})

        spreadsheet = client.open_by_key(MAIN_SHEET_ID)
        worksheet = spreadsheet.worksheet(MAIN_SHEET_NAME)

        # Clear existing data
        worksheet.clear()
        logger.info("GOOGLE_SHEETS_SETUP | Cleared existing data")

        # Define comprehensive headers (47 columns - added Transcription Status & Project Name)
        headers = [
            "OCD Number", "Audio Code", "Video ID", "Title", "Description",
            "Video Type", "Distribution Type", "Component", "Language", "Platform",
            "Project Date", "Publish Date", "Project Name", "Duration", "Total Duration",
            "URL", "Transcription File", "Transcription Status", "Backup Type", "Soft Renamed",
            "Content Tags", "Additional Notes", "Keywords", "Category", "Subcategory",
            "Resolution", "Format", "File Size", "Audio Quality", "Video Quality",
            "Executor Name", "Executor Timestamp", "Cross-Checker Name", "Cross-Checker Timestamp",
            "Validation Status", "Editing Required", "Editor Name", "Editor Timestamp", "Final Status",
            "With Logo", "Influencer",
            "Processing ID", "Folder Path", "Final Folder Path", "Created Date", "Last Modified", "System Notes"
        ]

        # Insert headers
        worksheet.update('A1', [headers])
        logger.info(f"GOOGLE_SHEETS_SETUP | Inserted {len(headers)} headers")

        # Format headers (A1:AU1 for 47 columns)
        try:
            worksheet.format('A1:AU1', {
                "backgroundColor": {"red": 0.26, "green": 0.52, "blue": 0.96},
                "textFormat": {"bold": True, "foregroundColor": {"red": 1.0, "green": 1.0, "blue": 1.0}},
                "horizontalAlignment": "CENTER"
            })
            logger.info("GOOGLE_SHEETS_SETUP | Headers formatted successfully")
        except Exception as e:
            logger.warning(f"GOOGLE_SHEETS_SETUP | Header formatting failed: {e}")

        # Step 2: Create comprehensive test data in database
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get executor1 ID
        cursor.execute("SELECT id FROM users WHERE username = 'executor1'")
        result = cursor.fetchone()
        if not result:
            return jsonify({'success': False, 'error': 'Executor1 user not found'})

        executor_id = result[0]

        # Clear existing test data
        cursor.execute("DELETE FROM processing_queue WHERE folder_name LIKE '%Google_Sheets_Setup%'")
        cursor.execute("DELETE FROM verification_queue WHERE processing_id NOT IN (SELECT id FROM processing_queue)")

        # Create comprehensive test metadata
        test_entries = [
            {
                "folder_name": "Google_Sheets_Setup_Test_001",
                "folder_path": "/test/Google_Sheets_Setup_Test_001",
                "metadata": {
                    "ocd_number": "OCD-SETUP-001",
                    "audio_code": "AUDIO-SETUP-001",
                    "video_id": "SETUP-TEST-001",
                    "title": "Google Sheets Setup Test - Sadhguru Wisdom Talk",
                    "description": "Comprehensive test of Google Sheets setup with all 43 metadata fields populated and validated through the Flask app.",
                    "video_type": "Talk",
                    "distribution_type": "social",
                    "component": "Sadhguru",
                    "language": "English",
                    "platform": "YouTube",
                    "project_date": "2024-07-04",
                    "publish_date": "2024-07-05",
                    "duration": "42:30",
                    "total_duration": "42:30",
                    "url": "https://youtube.com/watch?v=setuptest001",
                    "transcription_file": "setup_test_transcript.txt",
                    "backup_type": "Master Copy",
                    "soft_renamed": "Google_Sheets_Setup_Test_Sadhguru_Wisdom_Talk_2024",
                    "content_tags": ["spirituality", "wisdom", "setup test"],
                    "additional_notes": "Comprehensive test entry created through Flask app for Google Sheets validation",
                    "keywords": "sadhguru, spirituality, wisdom, google sheets setup test",
                    "category": "Spiritual Discourse",
                    "subcategory": "Setup Test",
                    "resolution": "1920x1080",
                    "format": "MP4",
                    "file_size": "3.5 GB",
                    "audio_quality": "320 kbps",
                    "video_quality": "1080p HD",
                    "with_logo": "Yes",
                    "influencer": "No"
                }
            },
            {
                "folder_name": "Google_Sheets_Setup_Test_002",
                "folder_path": "/test/Google_Sheets_Setup_Test_002",
                "metadata": {
                    "ocd_number": "OCD-SETUP-002",
                    "audio_code": "AUDIO-SETUP-002",
                    "video_id": "SETUP-TEST-002",
                    "title": "Google Sheets Setup Test - Isha Yoga Practice",
                    "description": "Second comprehensive test entry for Google Sheets setup focusing on yoga practice content.",
                    "video_type": "Practice Session",
                    "distribution_type": "internal",
                    "component": "Isha Yoga",
                    "language": "English",
                    "platform": "Internal Archive",
                    "project_date": "2024-07-04",
                    "duration": "28:15",
                    "total_duration": "28:15",
                    "url": "",
                    "transcription_file": "yoga_setup_test_transcript.txt",
                    "backup_type": "Working Copy",
                    "soft_renamed": "Google_Sheets_Setup_Test_Isha_Yoga_Practice_2024",
                    "content_tags": ["yoga", "practice", "setup test"],
                    "additional_notes": "Internal content setup test entry for Google Sheets validation",
                    "keywords": "isha yoga, practice, setup test",
                    "category": "Yoga Practice",
                    "subcategory": "Setup Test",
                    "resolution": "1920x1080",
                    "format": "MP4",
                    "file_size": "2.2 GB",
                    "audio_quality": "256 kbps",
                    "video_quality": "1080p HD",
                    "with_logo": "No",
                    "influencer": "Yes"
                }
            }
        ]

        processing_ids = []
        for entry in test_entries:
            cursor.execute('''
                INSERT INTO processing_queue (folder_name, folder_path, metadata, executor_id, status, processed_at)
                VALUES (?, ?, ?, ?, 'completed', CURRENT_TIMESTAMP)
            ''', (
                entry["folder_name"],
                entry["folder_path"],
                json.dumps(entry["metadata"]),
                executor_id
            ))

            processing_id = cursor.lastrowid
            processing_ids.append(processing_id)

            # Add to verification queue
            cursor.execute('''
                INSERT INTO verification_queue (processing_id, status)
                VALUES (?, 'pending')
            ''', (processing_id,))

            logger.info(f"GOOGLE_SHEETS_SETUP | Created test entry: {entry['folder_name']} (ID: {processing_id})")

        conn.commit()
        conn.close()

        logger.info(f"GOOGLE_SHEETS_SETUP | Setup completed successfully")

        return jsonify({
            'success': True,
            'message': 'Google Sheets setup completed successfully',
            'details': {
                'headers_created': len(headers),
                'test_entries_created': len(test_entries),
                'processing_ids': processing_ids,
                'sheet_url': f'https://docs.google.com/spreadsheets/d/{MAIN_SHEET_ID}'
            }
        })

    except Exception as e:
        logger.error(f"GOOGLE_SHEETS_SETUP | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/admin/test_complete_workflow', methods=['POST'])
@login_required
@role_required(['Admin'])
def test_complete_workflow():
    """Test the complete workflow including Cross-Checker validation and Google Sheets upload"""
    try:
        logger.info("COMPLETE_WORKFLOW_TEST | Starting comprehensive workflow test")

        # Get the first pending verification
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT vq.processing_id, pq.folder_name, pq.metadata
            FROM verification_queue vq
            JOIN processing_queue pq ON vq.processing_id = pq.id
            WHERE vq.status = 'pending'
            ORDER BY vq.id ASC
            LIMIT 1
        ''')

        result = cursor.fetchone()
        if not result:
            return jsonify({'success': False, 'error': 'No pending verification found'})

        processing_id, folder_name, metadata_json = result
        metadata = json.loads(metadata_json)

        logger.info(f"COMPLETE_WORKFLOW_TEST | Testing with Processing ID: {processing_id}, Folder: {folder_name}")

        # Simulate Cross-Checker validation with comprehensive metadata updates
        updated_metadata = metadata.copy()
        updated_metadata.update({
            "title": f"{metadata.get('title', '')} - VALIDATED by Automated Test",
            "description": f"{metadata.get('description', '')} This entry has been validated through automated testing and should appear in Google Sheets with all 43 fields populated correctly.",
            "additional_notes": "Automated Cross-Checker validation completed successfully. All metadata fields verified and updated through Flask app testing.",
            "keywords": f"{metadata.get('keywords', '')} automated test, validated, flask app",
            "category": f"{metadata.get('category', '')} - VALIDATED",
            "subcategory": f"{metadata.get('subcategory', '')} - AUTOMATED TEST"
        })

        # Update processing queue with validated metadata
        cursor.execute('''
            UPDATE processing_queue
            SET metadata = ?, status = 'validated'
            WHERE id = ?
        ''', (json.dumps(updated_metadata), processing_id))

        # Update verification queue
        cursor.execute('''
            UPDATE verification_queue
            SET status = 'approved', notes = 'Automated test validation', verified_at = CURRENT_TIMESTAMP
            WHERE processing_id = ?
        ''', (processing_id,))

        # Log system action
        log_system_action(cursor, 1, 'AUTOMATED_VALIDATION', f'Processing ID: {processing_id}, Folder: {folder_name}')

        conn.commit()
        conn.close()

        # Upload to Google Sheets
        upload_success = upload_to_google_sheets(
            updated_metadata,
            'executor1',
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'automated_test',
            False  # editing_required
        )

        if upload_success:
            logger.info(f"COMPLETE_WORKFLOW_TEST | Google Sheets upload successful for Processing ID: {processing_id}")

            # Verify Google Sheets entry
            try:
                client = init_google_sheets()
                if client:
                    spreadsheet = client.open_by_key(MAIN_SHEET_ID)
                    worksheet = spreadsheet.worksheet(MAIN_SHEET_NAME)
                    all_values = worksheet.get_all_values()

                    # Look for our test entry
                    test_found = False
                    for row in all_values[1:]:  # Skip header row
                        if len(row) > 3 and updated_metadata.get('title', '') in row[3]:
                            test_found = True
                            break

                    verification_status = "Entry found in Google Sheets" if test_found else "Entry not found in Google Sheets"
                else:
                    verification_status = "Could not verify Google Sheets"
            except Exception as e:
                verification_status = f"Verification error: {str(e)}"

            return jsonify({
                'success': True,
                'message': 'Complete workflow test completed successfully',
                'details': {
                    'processing_id': processing_id,
                    'folder_name': folder_name,
                    'metadata_fields': len(updated_metadata),
                    'google_sheets_upload': 'Success',
                    'google_sheets_verification': verification_status,
                    'sheet_url': f'https://docs.google.com/spreadsheets/d/{MAIN_SHEET_ID}'
                }
            })
        else:
            logger.error(f"COMPLETE_WORKFLOW_TEST | Google Sheets upload failed for Processing ID: {processing_id}")
            return jsonify({
                'success': False,
                'error': 'Google Sheets upload failed',
                'details': {
                    'processing_id': processing_id,
                    'folder_name': folder_name
                }
            })

    except Exception as e:
        logger.error(f"COMPLETE_WORKFLOW_TEST | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/test_google_sheets')
@login_required
@role_required(['Admin'])
def test_google_sheets_page():
    """Test page for Google Sheets integration"""
    return render_template('test_google_sheets.html')

@app.route('/test_crosschecker_buttons')
@login_required
@role_required(['Admin', 'Cross-Checker'])
def test_crosschecker_buttons_page():
    """Test page for Cross-Checker button functionality"""
    return render_template('test_crosschecker_buttons.html')

@app.route('/api/update_metadata/<int:processing_id>', methods=['POST'])
@login_required
@role_required(['Cross-Checker', 'Admin'])
def update_metadata(processing_id):
    """Update metadata for a processing entry"""
    try:
        data = request.get_json()
        updated_metadata = data.get('metadata', {})

        logger.info(f"UPDATE_METADATA | Processing ID: {processing_id} | User: {session.get('username')}")

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Update metadata in processing_queue
        cursor.execute('''
            UPDATE processing_queue
            SET metadata = ?
            WHERE id = ?
        ''', (json.dumps(updated_metadata), processing_id))

        if cursor.rowcount == 0:
            return jsonify({'success': False, 'error': 'Processing entry not found'})

        # Log the update
        log_system_action(cursor, session.get('user_id'), 'METADATA_UPDATE', f'Processing ID: {processing_id}')

        conn.commit()
        conn.close()

        logger.info(f"UPDATE_METADATA | SUCCESS | Processing ID: {processing_id}")
        return jsonify({'success': True, 'message': 'Metadata updated successfully'})

    except Exception as e:
        logger.error(f"UPDATE_METADATA | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/search_google_sheets_crosschecker', methods=['POST'])
@login_required
@role_required(['Cross-Checker', 'Admin'])
def search_google_sheets_crosschecker():
    """Search Google Sheets for video data to auto-fill metadata"""
    try:
        data = request.get_json()
        video_id = data.get('video_id', '').strip()

        if not video_id:
            return jsonify({'success': False, 'error': 'Video ID is required'})

        logger.info(f"GOOGLE_SHEETS_SEARCH | Video ID: {video_id} | User: {session.get('username')}")

        # Initialize Google Sheets client
        client = init_google_sheets()
        if not client:
            return jsonify({'success': False, 'error': 'Failed to connect to Google Sheets'})

        # Search in the specific sheet with multiple worksheets
        SEARCH_SHEET_ID = '1yw98-IkQB33U6gsZuhKDtiGaolTrEQM-s6Ty5fF6Tj4'
        SHEET_NAMES = ['SG', 'IF', 'IG']

        spreadsheet = client.open_by_key(SEARCH_SHEET_ID)

        found_data = None
        found_sheet = None

        # Search in each sheet
        for sheet_name in SHEET_NAMES:
            try:
                logger.info(f"GOOGLE_SHEETS_SEARCH | Searching in sheet: {sheet_name}")
                worksheet = spreadsheet.worksheet(sheet_name)
                all_values = worksheet.get_all_values()

                if len(all_values) < 2:  # No data rows
                    continue

                # Search in Column C (index 2) for Video ID
                for row_index, row in enumerate(all_values[1:], start=2):  # Skip header row
                    if len(row) > 2 and row[2] == video_id:  # Column C is index 2
                        found_data = row
                        found_sheet = sheet_name
                        logger.info(f"GOOGLE_SHEETS_SEARCH | Found Video ID in sheet: {sheet_name}, row: {row_index}")
                        break

                if found_data:
                    break

            except Exception as sheet_error:
                logger.warning(f"GOOGLE_SHEETS_SEARCH | Error accessing sheet {sheet_name}: {str(sheet_error)}")
                continue

        if not found_data:
            return jsonify({'success': False, 'error': f'Video ID "{video_id}" not found in any sheet (SG, IF, IG)'})

        # Map the found data according to column specifications
        result_data = {}

        # Column mappings (0-based index)
        # A=0, B=1, C=2, D=3, E=4, F=5, G=6, H=7, I=8, J=9, K=10, L=11

        # Title → Column D (index 3)
        if len(found_data) > 3 and found_data[3]:
            result_data['title'] = found_data[3]

        # Date → Column B (index 1) - Convert from ISO format
        if len(found_data) > 1 and found_data[1]:
            try:
                # Convert from ISO format like 2025-06-30T06:42:24Z to 30-June-2025
                iso_date = found_data[1]
                if 'T' in iso_date:
                    date_part = iso_date.split('T')[0]  # Get date part before T
                    year, month, day = date_part.split('-')

                    # Month names
                    month_names = ['', 'January', 'February', 'March', 'April', 'May', 'June',
                                 'July', 'August', 'September', 'October', 'November', 'December']

                    month_name = month_names[int(month)]
                    formatted_date = f"{int(day)}-{month_name}-{year}"
                    result_data['date'] = formatted_date
                else:
                    result_data['date'] = found_data[1]  # Use as-is if not ISO format
            except:
                result_data['date'] = found_data[1]  # Fallback to original

        # Description → Column G (index 6)
        if len(found_data) > 6 and found_data[6]:
            result_data['description'] = found_data[6]

        # URL → Column H (index 7)
        if len(found_data) > 7 and found_data[7]:
            result_data['url'] = found_data[7]

        # Duration → Column I (index 8) - Convert 0:01:54 → 01Min-54Secs
        if len(found_data) > 8 and found_data[8]:
            try:
                duration = found_data[8]
                if ':' in duration:
                    parts = duration.split(':')
                    if len(parts) == 3:  # H:MM:SS
                        hours, minutes, seconds = parts
                        if int(hours) > 0:
                            result_data['duration'] = f"{int(hours):02d}Hrs-{int(minutes):02d}Mins-{int(seconds):02d}Secs"
                        else:
                            result_data['duration'] = f"{int(minutes):02d}Mins-{int(seconds):02d}Secs"
                    elif len(parts) == 2:  # MM:SS
                        minutes, seconds = parts
                        result_data['duration'] = f"{int(minutes):02d}Mins-{int(seconds):02d}Secs"
                    else:
                        result_data['duration'] = duration
                else:
                    result_data['duration'] = duration
            except:
                result_data['duration'] = found_data[8]  # Fallback to original

        # Transcription File Name → Column L (index 11)
        if len(found_data) > 11 and found_data[11]:
            result_data['transcription_file'] = found_data[11]

        # Published Platform (auto-populate based on sheet)
        platform_mapping = {
            'SG': 'Sadhguru YouTube',
            'IF': 'Isha Foundation YouTube',
            'IG': 'Sadhguru Instagram'
        }
        result_data['platform'] = platform_mapping.get(found_sheet, 'Unknown')

        # Add sheet info for debugging
        result_data['found_in_sheet'] = found_sheet

        logger.info(f"GOOGLE_SHEETS_SEARCH | SUCCESS | Found data for Video ID: {video_id} in sheet: {found_sheet}")
        return jsonify({'success': True, 'data': result_data})

    except Exception as e:
        logger.error(f"GOOGLE_SHEETS_SEARCH | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/detect_video_duration', methods=['POST', 'OPTIONS'])
def detect_video_duration():
    logger.info(f"VIDEO_DURATION | Request received: {request.method} from {request.remote_addr}")

    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        logger.info("VIDEO_DURATION | Handling OPTIONS preflight request")
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response

    logger.info(f"VIDEO_DURATION | POST request - Session: {session.get('user_id')} Role: {session.get('role')}")

    # Apply decorators only for POST requests
    if not session.get('user_id'):
        logger.warning("VIDEO_DURATION | Authentication required")
        return jsonify({'success': False, 'error': 'Authentication required'}), 401

    if session.get('role') not in ['Executor', 'Admin']:
        logger.warning(f"VIDEO_DURATION | Insufficient permissions for role: {session.get('role')}")
        return jsonify({'success': False, 'error': 'Insufficient permissions'}), 403
    """Detect video duration using pymediainfo"""
    try:
        logger.info("VIDEO_DURATION | Starting file processing...")

        if 'video_file' not in request.files:
            logger.error("VIDEO_DURATION | No video_file in request.files")
            return jsonify({'success': False, 'error': 'No video file provided'})

        file = request.files['video_file']
        if file.filename == '':
            logger.error("VIDEO_DURATION | Empty filename")
            return jsonify({'success': False, 'error': 'No file selected'})

        logger.info(f"VIDEO_DURATION | Processing file: {file.filename} | User: {session.get('username')}")
        logger.info(f"VIDEO_DURATION | File object type: {type(file)}")
        logger.info(f"VIDEO_DURATION | Request files keys: {list(request.files.keys())}")

        # Check file extension
        allowed_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv'}
        file_ext = os.path.splitext(file.filename)[1].lower()

        if file_ext not in allowed_extensions:
            return jsonify({'success': False, 'error': f'Unsupported file format: {file_ext}'})

        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)

        try:
            logger.info(f"VIDEO_DURATION | Saving temp file: {temp_file_path}")

            # Save file in chunks to handle large files
            chunk_size = 8192
            with open(temp_file_path, 'wb') as temp_file:
                while True:
                    chunk = file.read(chunk_size)
                    if not chunk:
                        break
                    temp_file.write(chunk)

            logger.info(f"VIDEO_DURATION | File saved successfully: {os.path.getsize(temp_file_path)} bytes")

            # Use pymediainfo to detect duration (lightweight and fast)
            duration = get_video_duration_pymediainfo(temp_file_path)

            if duration:
                logger.info(f"VIDEO_DURATION | SUCCESS | Duration: {duration} for {file.filename}")
                return jsonify({'success': True, 'duration': duration, 'filename': file.filename})
            else:
                logger.error(f"VIDEO_DURATION | FAILED | Could not detect duration for {file.filename}")
                return jsonify({'success': False, 'error': 'Could not detect video duration. Please check if the file is a valid video format.'})

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    logger.info(f"VIDEO_DURATION | Removed temp file: {temp_file_path}")
                if os.path.exists(temp_dir):
                    os.rmdir(temp_dir)
                    logger.info(f"VIDEO_DURATION | Removed temp directory: {temp_dir}")
            except Exception as cleanup_error:
                logger.warning(f"VIDEO_DURATION | Cleanup warning: {cleanup_error}")

    except Exception as e:
        logger.error(f"VIDEO_DURATION | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': f'Server error: {str(e)}'})

@app.route('/api/test_video_duration', methods=['POST', 'OPTIONS'])
def test_video_duration():
    """Test video duration endpoint without authentication"""
    print(f"🔧 TEST_VIDEO_DURATION | Request: {request.method} from {request.remote_addr}")

    if request.method == 'OPTIONS':
        print("🔧 TEST_VIDEO_DURATION | Handling OPTIONS request")
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response

    try:
        print("🔧 TEST_VIDEO_DURATION | Processing POST request")

        if 'video_file' not in request.files:
            print("🔧 TEST_VIDEO_DURATION | No video_file in request")
            return jsonify({'success': False, 'error': 'No video file provided'})

        file = request.files['video_file']
        if file.filename == '':
            print("🔧 TEST_VIDEO_DURATION | Empty filename")
            return jsonify({'success': False, 'error': 'No file selected'})

        print(f"🔧 TEST_VIDEO_DURATION | File: {file.filename}")

        # Just return success without processing for now
        return jsonify({
            'success': True,
            'filename': file.filename,
            'duration': '02Mins-30Secs',
            'message': 'Test endpoint working - file received successfully'
        })

    except Exception as e:
        print(f"🔧 TEST_VIDEO_DURATION | Error: {str(e)}")
        return jsonify({'success': False, 'error': f'Test error: {str(e)}'})

@app.route('/api/detect_duration_from_path', methods=['POST'])
@login_required
@role_required(['Executor 1', 'Executor 2', 'Executor 3', 'Admin'])
def detect_duration_from_path():
    """Detect video duration from server file path using pymediainfo"""
    try:
        # Debug session information
        logger.info(f"DURATION_FROM_PATH | Session user_id: {session.get('user_id')}")
        logger.info(f"DURATION_FROM_PATH | Session username: {session.get('username')}")
        logger.info(f"DURATION_FROM_PATH | Session role: {session.get('role')}")

        data = request.get_json()
        file_path = data.get('file_path', '')

        if not file_path:
            return jsonify({'success': False, 'error': 'No file path provided'})

        logger.info(f"DURATION_FROM_PATH | File: {file_path} | User: {session.get('username')}")

        # Security check: Ensure file is within E:\ drive
        if not file_path.upper().startswith('E:\\'):
            logger.warning(f"DURATION_FROM_PATH | SECURITY | Attempted access outside E:\\ drive: {file_path}")
            return jsonify({'success': False, 'error': 'Access restricted to E:\\ drive only'})

        # Check if file exists
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': 'File does not exist'})

        if not os.path.isfile(file_path):
            return jsonify({'success': False, 'error': 'Path is not a file'})

        # Check file extension
        allowed_extensions = {'.mov', '.mp4', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv'}
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext not in allowed_extensions:
            return jsonify({'success': False, 'error': f'Unsupported file format: {file_ext}'})

        # Get duration using pymediainfo
        duration = get_video_duration_pymediainfo(file_path)

        if duration:
            # Format duration according to specifications
            formatted_duration = formatDuration(duration)

            logger.info(f"DURATION_FROM_PATH | SUCCESS | Duration: {duration} → {formatted_duration} for {file_path}")
            return jsonify({
                'success': True,
                'duration': formatted_duration,
                'raw_duration': duration,
                'filename': os.path.basename(file_path),
                'file_path': file_path
            })
        else:
            logger.error(f"DURATION_FROM_PATH | FAILED | Could not detect duration for {file_path}")
            return jsonify({'success': False, 'error': 'Could not detect video duration'})

    except Exception as e:
        logger.error(f"DURATION_FROM_PATH | ERROR | {str(e)}")
        return jsonify({'success': False, 'error': f'Server error: {str(e)}'})

def formatDuration(duration_str):
    """Format duration from HH:MM:SS to XXMins-XXSecs"""
    try:
        # Parse duration (expected format: HH:MM:SS)
        parts = duration_str.split(':')
        if len(parts) != 3:
            return duration_str  # Return as-is if not in expected format

        hours, minutes, seconds = int(parts[0]), int(parts[1]), int(parts[2])

        # Convert hours to minutes
        total_minutes = hours * 60 + minutes

        # Format according to rules
        if total_minutes == 0 and seconds > 0:
            # Less than a minute: "53Secs"
            return f"{seconds:02d}{'Sec' if seconds == 1 else 'Secs'}"
        elif total_minutes > 0:
            # Has minutes
            min_text = 'Min' if total_minutes == 1 else 'Mins'
            sec_text = 'Sec' if seconds == 1 else 'Secs'

            if seconds == 0:
                # Exact minutes: "05Mins"
                return f"{total_minutes:02d}{min_text}"
            else:
                # Minutes and seconds: "02Mins-01Sec" or "72Mins-54Secs"
                return f"{total_minutes:02d}{min_text}-{seconds:02d}{sec_text}"
        else:
            # Zero duration
            return '00Secs'

    except Exception as e:
        logger.error(f"DURATION_FORMAT | ERROR | {str(e)}")
        return duration_str  # Return original if formatting fails

@app.route('/api/test_auth', methods=['GET', 'POST'])
@login_required
def test_auth():
    """Test authentication and session info"""
    return jsonify({
        'success': True,
        'user_id': session.get('user_id'),
        'username': session.get('username'),
        'role': session.get('role'),
        'message': 'Authentication working'
    })

def get_video_duration_pymediainfo(video_path):
    """Get video duration using pymediainfo (lightweight and fast)"""
    try:
        from pymediainfo import MediaInfo

        logger.info(f"VIDEO_DURATION | Using pymediainfo for: {video_path}")
        logger.info(f"VIDEO_DURATION | File exists: {os.path.exists(video_path)}")
        logger.info(f"VIDEO_DURATION | File size: {os.path.getsize(video_path)} bytes")

        # Parse media file
        media_info = MediaInfo.parse(video_path)

        # Look for video tracks
        for track in media_info.tracks:
            if track.track_type == 'Video':
                duration_ms = track.duration
                if duration_ms:
                    # Convert milliseconds to seconds
                    duration_seconds = int(duration_ms / 1000)

                    # Convert to HH:MM:SS format
                    hours = duration_seconds // 3600
                    minutes = (duration_seconds % 3600) // 60
                    seconds = duration_seconds % 60

                    duration = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

                    logger.info(f"VIDEO_DURATION | pymediainfo detected duration: {duration} ({duration_seconds} seconds)")
                    return duration

        # If no video track found, try general track
        for track in media_info.tracks:
            if track.track_type == 'General':
                duration_ms = track.duration
                if duration_ms:
                    # Convert milliseconds to seconds
                    duration_seconds = int(duration_ms / 1000)

                    # Convert to HH:MM:SS format
                    hours = duration_seconds // 3600
                    minutes = (duration_seconds % 3600) // 60
                    seconds = duration_seconds % 60

                    duration = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

                    logger.info(f"VIDEO_DURATION | pymediainfo detected duration from general track: {duration}")
                    return duration

        logger.error(f"VIDEO_DURATION | No duration found in media tracks")
        return None

    except ImportError:
        logger.error(f"VIDEO_DURATION | pymediainfo not installed")
        return None
    except Exception as e:
        logger.error(f"VIDEO_DURATION | pymediainfo error: {str(e)}")
        return None

@app.route('/api/test_simple', methods=['GET', 'POST', 'OPTIONS'])
def test_simple():
    """Simple test endpoint without authentication"""
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response

    return jsonify({
        'success': True,
        'method': request.method,
        'message': 'Simple test endpoint working',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/test_mediainfo', methods=['GET'])
@login_required
def test_mediainfo():
    """Test if pymediainfo is available"""
    try:
        from pymediainfo import MediaInfo

        # Test basic functionality
        return jsonify({
            'success': True,
            'mediainfo_available': True,
            'version': 'MediaInfo Library (pymediainfo)',
            'library': 'pymediainfo'
        })

    except ImportError:
        return jsonify({
            'success': False,
            'mediainfo_available': False,
            'error': 'pymediainfo library not installed'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'mediainfo_available': False,
            'error': str(e)
        })

@app.route('/api/crosscheck/validate', methods=['POST'])
@login_required
@role_required(['Cross-Checker', 'Admin'])
def crosscheck_validate():
    """Validate folder via Cross-Checker (alternative endpoint)"""
    try:
        data = request.get_json()
        processing_id = data.get('processing_id')

        if not processing_id:
            return jsonify({'success': False, 'error': 'Processing ID is required'})

        # Forward to main validation function
        return validate_folder(processing_id)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/crosscheck/delete', methods=['POST'])
@login_required
@role_required(['Cross-Checker', 'Admin'])
def crosscheck_delete():
    """Delete folder via Cross-Checker (alternative endpoint)"""
    try:
        data = request.get_json()
        processing_id = data.get('processing_id')

        if not processing_id:
            return jsonify({'success': False, 'error': 'Processing ID is required'})

        return delete_processed_folder(processing_id)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/delete_processed_folder/<int:processing_id>', methods=['DELETE'])
@login_required
@role_required(['Cross-Checker', 'Admin'])
def delete_processed_folder(processing_id):
    """Delete a processed folder from filesystem and mark as deleted"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get folder information
        cursor.execute('''
            SELECT pq.folder_name, pq.folder_path, vq.final_folder_path, vq.status
            FROM processing_queue pq
            LEFT JOIN verification_queue vq ON pq.id = vq.processing_id
            WHERE pq.id = ?
        ''', (processing_id,))

        result = cursor.fetchone()
        if not result:
            return jsonify({'success': False, 'error': 'Folder not found'})

        folder_name, original_path, final_path, status = result

        # Check if folder is verified (only allow deletion of verified folders)
        if status != 'verified':
            return jsonify({'success': False, 'error': 'Can only delete verified folders'})

        # Determine which folder path to delete
        folder_to_delete = final_path if final_path and os.path.exists(final_path) else original_path

        # Delete folder from filesystem
        if folder_to_delete and os.path.exists(folder_to_delete):
            try:
                import shutil
                shutil.rmtree(folder_to_delete)
                folder_deleted = True
            except Exception as e:
                print(f"Error deleting folder {folder_to_delete}: {e}")
                folder_deleted = False
        else:
            folder_deleted = True  # Folder doesn't exist, consider it deleted

        # Mark as deleted in database
        cursor.execute('''
            UPDATE verification_queue
            SET status = 'deleted', notes = COALESCE(notes, '') || ' [DELETED by ' || ? || ']'
            WHERE processing_id = ?
        ''', (session.get('username'), processing_id))

        cursor.execute('UPDATE processing_queue SET status = "deleted" WHERE id = ?', (processing_id,))

        # Log the deletion
        log_system_action(cursor, session.get('user_id'), 'FOLDER_DELETED',
                         f'Folder: {folder_name}, Path: {folder_to_delete}, Filesystem Deleted: {folder_deleted}')

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'Folder "{folder_name}" deleted successfully',
            'filesystem_deleted': folder_deleted
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/test_google_sheets', methods=['GET'])
@login_required
@role_required(['Admin'])
def test_google_sheets_connection():
    """Test Google Sheets connection and access"""
    try:
        logger.info(f"GOOGLE_SHEETS_TEST | Starting connection test...")

        # Test client initialization
        client = init_google_sheets()
        if not client:
            return jsonify({
                'success': False,
                'error': 'Failed to initialize Google Sheets client',
                'sheet_id': MAIN_SHEET_ID,
                'sheet_name': MAIN_SHEET_NAME
            })

        logger.info(f"GOOGLE_SHEETS_TEST | Client initialized successfully")

        # Test spreadsheet access
        spreadsheet = client.open_by_key(MAIN_SHEET_ID)
        logger.info(f"GOOGLE_SHEETS_TEST | Spreadsheet opened: {spreadsheet.title}")

        # Test worksheet access
        worksheet = spreadsheet.worksheet(MAIN_SHEET_NAME)
        logger.info(f"GOOGLE_SHEETS_TEST | Worksheet accessed: {worksheet.title}")

        # Test reading data
        all_values = worksheet.get_all_values()
        row_count = len(all_values)

        logger.info(f"GOOGLE_SHEETS_TEST | Successfully read {row_count} rows")

        return jsonify({
            'success': True,
            'message': 'Google Sheets connection successful',
            'details': {
                'sheet_id': MAIN_SHEET_ID,
                'sheet_name': MAIN_SHEET_NAME,
                'spreadsheet_title': spreadsheet.title,
                'worksheet_title': worksheet.title,
                'row_count': row_count,
                'connection_status': 'Active'
            }
        })

    except Exception as e:
        logger.error(f"GOOGLE_SHEETS_TEST | ERROR | {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'error_type': type(e).__name__,
            'sheet_id': MAIN_SHEET_ID,
            'sheet_name': MAIN_SHEET_NAME
        })

if __name__ == '__main__':
    print("🔧 ARCHIVES MEDIAFLOW PRO - STARTUP")
    print("="*40)

    try:
        print("Step 1: Initializing database...")
        init_database()
        print("✅ Database initialized successfully")

        print("Step 1.5: Ensuring required directories...")
        ensure_required_directories()
        print("✅ Required directories ensured")

        print("Step 2: Testing Google Sheets connection...")
        gc = init_google_sheets()
        if gc:
            print("✅ Google Sheets connection successful")
        else:
            print("⚠️ Google Sheets connection failed - continuing without it")

        print("Step 3: Starting Flask application...")
        print("🚀 Archives MediaFlow Pro starting...")
        print("📍 URL: http://localhost:5005")
        print("🔐 Default credentials:")
        print("   executor1/password123, executor2/password123")
        print("   crosschecker/password123, editor/password123, admin/password123")
        print("🎯 Admin Features: User Management, Performance Analytics, Live Activity Feed")
        print("📊 Complete Workflow: Executor → Cross-Checker → Editor → Admin Oversight")
        print("✅ Starting server...")

        app.run(debug=True, host='0.0.0.0', port=5005)

    except Exception as e:
        print(f"❌ Startup error: {e}")
        import traceback
        traceback.print_exc()
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Check if port 5005 is already in use")
        print("2. Verify credentials.json file exists and is valid")
        print("3. Ensure all dependencies are installed: python -m pip install -r requirements.txt")
        print("4. Check database file permissions")
        exit(1)
