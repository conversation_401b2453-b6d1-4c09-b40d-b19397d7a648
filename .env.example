# Archives MediaFlow Pro - Environment Configuration Template
# Copy this file to .env and update with your actual values

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-super-secret-key-here-change-this-in-production

# Database Configuration
DATABASE_PATH=archives_mediaflow_pro.db

# Google Sheets Configuration
GOOGLE_SHEET_SEARCH_ID=1yw98-IkQB33U6gsZuhKDtiGaolTrEQM-s6Ty5fF6Tj4
GOOGLE_SHEET_UPLOAD_ID=12vG_869ivHGQmPRcXib03ZqQ1KbErGwgKw9TG-txuQ4
GOOGLE_CREDENTIALS_FILE=credentials.json

# Server Configuration
HOST=0.0.0.0
PORT=5005

# File Processing Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=1073741824  # 1GB in bytes
ALLOWED_EXTENSIONS=mov,mp4,avi,mkv

# Audio Processing
FFMPEG_PATH=ffmpeg  # Path to FFMPEG executable
AUDIO_OUTPUT_FORMAT=wav
AUDIO_SAMPLE_RATE=44100
AUDIO_CHANNELS=2

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=archives_mediaflow_pro.log
LOG_MAX_BYTES=10485760  # 10MB
LOG_BACKUP_COUNT=5

# Security Configuration
SESSION_TIMEOUT=3600  # 1 hour in seconds
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900  # 15 minutes in seconds
