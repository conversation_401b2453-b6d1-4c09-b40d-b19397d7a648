// Editor Dashboard JavaScript

let currentEditingId = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeEditorDashboard();
});

function initializeEditorDashboard() {
    console.log('Editor Dashboard initialized');
    
    // Refresh statistics every 30 seconds
    setInterval(refreshStatistics, 30000);
}

function startEditing(editingId, folderName) {
    if (!confirm(`Start editing "${folderName}"?`)) {
        return;
    }
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Starting...';
    button.disabled = true;
    
    fetch(`/api/start_editing/${editingId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Editing started successfully!');
            
            // Refresh page to update status
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert('error', data.error || 'Error starting editing.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Network error while starting editing.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function viewEditingDetails(editingId, folderName, metadataJson) {
    try {
        const metadata = JSON.parse(metadataJson || '{}');
        
        // Create details content
        const detailsHTML = createEditingDetailsHTML(metadata, folderName);
        document.getElementById('editingDetailsContent').innerHTML = detailsHTML;
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('editingDetailsModal'));
        modal.show();
        
    } catch (error) {
        console.error('Error viewing editing details:', error);
        showAlert('error', 'Error loading editing details.');
    }
}

function createEditingDetailsHTML(metadata, folderName) {
    return `
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card bg-light">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-folder text-warning me-2"></i>${folderName}
                        </h5>
                        <p class="card-text text-muted">Review content metadata and editing requirements</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-warning mb-3">
                    <i class="fas fa-info-circle me-2"></i>Content Information
                </h6>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">OCD Number:</label>
                    <div class="form-control-plaintext border rounded p-2 bg-light">
                        ${metadata.ocd_number || 'Not specified'}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Audio Code:</label>
                    <div class="form-control-plaintext border rounded p-2 bg-light">
                        ${metadata.audio_code || 'Not specified'}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Video Type:</label>
                    <div class="form-control-plaintext border rounded p-2 bg-light">
                        <span class="badge bg-warning">${metadata.video_type || 'Not specified'}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Component:</label>
                    <div class="form-control-plaintext border rounded p-2 bg-light">
                        ${metadata.component || 'Not specified'}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Language:</label>
                    <div class="form-control-plaintext border rounded p-2 bg-light">
                        ${metadata.language || 'Not specified'}
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h6 class="text-warning mb-3">
                    <i class="fas fa-cogs me-2"></i>Technical Details
                </h6>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Total Duration:</label>
                    <div class="form-control-plaintext border rounded p-2 bg-light">
                        ${metadata.total_duration || 'Not specified'}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Backup Type:</label>
                    <div class="form-control-plaintext border rounded p-2 bg-light">
                        <span class="badge bg-secondary">${metadata.backup_type || 'Not specified'}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Content Tags:</label>
                    <div class="form-control-plaintext border rounded p-2 bg-light">
                        ${metadata.content_tags && metadata.content_tags.length > 0 
                            ? metadata.content_tags.map(tag => `<span class="badge bg-outline-warning me-1">${tag}</span>`).join('')
                            : 'No tags specified'}
                    </div>
                </div>
                
                ${metadata.distribution_type === 'social' ? createSocialMediaDetailsHTML(metadata) : ''}
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <h6 class="text-warning mb-3">
                    <i class="fas fa-file-signature me-2"></i>Generated Filename
                </h6>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Soft Renamed Field:</label>
                    <div class="form-control-plaintext border rounded p-3 bg-warning bg-opacity-10">
                        <code class="text-dark">${metadata.soft_renamed || 'Not generated'}</code>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createSocialMediaDetailsHTML(metadata) {
    return `
        <div class="mb-3">
            <label class="form-label fw-bold">Title:</label>
            <div class="form-control-plaintext border rounded p-2 bg-light">
                ${metadata.title || 'Not specified'}
            </div>
        </div>
        
        <div class="mb-3">
            <label class="form-label fw-bold">Platform:</label>
            <div class="form-control-plaintext border rounded p-2 bg-light">
                <span class="badge bg-success">${metadata.platform || 'Not specified'}</span>
            </div>
        </div>
        
        <div class="mb-3">
            <label class="form-label fw-bold">URL:</label>
            <div class="form-control-plaintext border rounded p-2 bg-light">
                ${metadata.url ? `<a href="${metadata.url}" target="_blank" class="text-decoration-none">${metadata.url}</a>` : 'Not specified'}
            </div>
        </div>
    `;
}

function completeEditing(editingId, folderName) {
    currentEditingId = editingId;

    // Set folder name in modal
    document.querySelector('#completeEditingModal .modal-title').innerHTML =
        `<i class="fas fa-check-circle me-2"></i>Complete Editing: ${folderName}`;

    // Reset form
    resetCompleteEditingForm();

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('completeEditingModal'));
    modal.show();

    // Set up confirm button
    document.getElementById('confirmCompleteBtn').onclick = function() {
        submitCompleteEditing();
    };

    // Set up admin approval checkbox listener
    document.getElementById('requestAdminApproval').addEventListener('change', updateAdminReviewSummary);

    // Set up form validation
    setupFormValidation();
}

function resetCompleteEditingForm() {
    // Clear all form fields
    document.getElementById('sourceFolder').value = '';
    document.getElementById('destinationFolder').value = '';
    document.getElementById('editingNotes').value = '';
    document.getElementById('adminReviewNotes').value = '';
    document.getElementById('requestAdminApproval').checked = true;

    // Reset progress steps
    document.querySelectorAll('.step').forEach(step => {
        step.classList.remove('active', 'completed');
    });
    document.querySelector('.step').classList.add('active');

    // Reset summary
    updateSummary();

    // Disable submit button
    document.getElementById('confirmCompleteBtn').disabled = true;
}

function setupFormValidation() {
    // Add event listeners for real-time validation
    document.getElementById('sourceFolder').addEventListener('input', validateForm);
    document.getElementById('destinationFolder').addEventListener('input', validateForm);
    document.getElementById('editingNotes').addEventListener('input', validateForm);
}

function validateForm() {
    const sourceFolder = document.getElementById('sourceFolder').value.trim();
    const destinationFolder = document.getElementById('destinationFolder').value.trim();
    const editingNotes = document.getElementById('editingNotes').value.trim();

    const isValid = sourceFolder && destinationFolder && editingNotes;

    // Update submit button
    const submitBtn = document.getElementById('confirmCompleteBtn');
    submitBtn.disabled = !isValid;

    // Update progress steps
    updateProgressSteps(sourceFolder, destinationFolder, isValid);

    // Update summary
    updateSummary();

    // Update ready status
    const readyStatus = document.getElementById('readyStatus');
    if (isValid) {
        readyStatus.textContent = 'Ready to submit';
        readyStatus.className = 'text-success';
        submitBtn.innerHTML = '<i class="fas fa-check-circle me-2"></i>Complete Editing & Move Folder';
    } else {
        readyStatus.textContent = 'Waiting for folder selection';
        readyStatus.className = 'text-warning';
        submitBtn.innerHTML = '<i class="fas fa-check-circle me-2"></i>Complete Editing & Move Folder';
    }
}

function updateProgressSteps(sourceFolder, destinationFolder, isValid) {
    const steps = document.querySelectorAll('.step');

    // Reset all steps
    steps.forEach(step => step.classList.remove('active', 'completed'));

    if (sourceFolder) {
        steps[0].classList.add('completed');
        if (destinationFolder) {
            steps[1].classList.add('completed');
            if (isValid) {
                steps[2].classList.add('active');
            } else {
                steps[2].classList.add('active');
            }
        } else {
            steps[1].classList.add('active');
        }
    } else {
        steps[0].classList.add('active');
    }
}

function updateSummary() {
    const sourceFolder = document.getElementById('sourceFolder').value.trim();
    const destinationFolder = document.getElementById('destinationFolder').value.trim();

    document.getElementById('sourceSummary').textContent = sourceFolder || 'Not selected';
    document.getElementById('destinationSummary').textContent = destinationFolder || 'Not selected';

    updateAdminReviewSummary();
}

function updateAdminReviewSummary() {
    const requestAdminApproval = document.getElementById('requestAdminApproval').checked;
    const adminReviewSummary = document.getElementById('adminReviewSummary');

    if (requestAdminApproval) {
        adminReviewSummary.textContent = 'Requested';
        adminReviewSummary.className = 'text-success';
    } else {
        adminReviewSummary.textContent = 'Not requested';
        adminReviewSummary.className = 'text-muted';
    }
}

function browseSourceFolder() {
    console.log('📁 Opening source folder browser...');
    openServerFileBrowser('folder', 'source');
}

function browseDestinationFolder() {
    console.log('📁 Opening destination folder browser...');
    openServerFileBrowser('folder', 'destination');
}

// EXACT COPY FROM CROSSCHECKER.JS - Server-side file browser functions
function openServerFileBrowser(browseType, targetType) {
    console.log(`🔍 Opening server file browser: ${browseType} for ${targetType}`);
    showFileBrowserModal(browseType, targetType);
}

function showFileBrowserModal(browseType, targetType) {
    const modalId = 'serverFileBrowserModal';

    // Remove existing modal if any
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const title = browseType === 'file' ? 'Select Video File' : 'Select Folder';

    const modalHTML = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-${browseType === 'file' ? 'file-video' : 'folder'} me-2"></i>
                            ${title}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-folder"></i>
                                </span>
                                <input type="text" class="form-control" id="currentBrowserPath" value="Computer" readonly>
                                <button class="btn btn-outline-secondary" onclick="navigateUp()" title="Go up">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                                <button class="btn btn-outline-primary" onclick="refreshBrowser()" title="Refresh">
                                    <i class="fas fa-sync"></i>
                                </button>
                            </div>
                        </div>
                        <div id="fileBrowserContent" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 10px;">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="mt-2">Loading directory...</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                ${browseType === 'file' ?
                                  'Click on a video file (.mov, .mp4, .avi) to select it.' :
                                  'Navigate to the desired folder and click "Select Current Folder".'}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        ${browseType === 'folder' ?
                          `<button type="button" class="btn btn-primary" onclick="selectCurrentFolder()">
                             <i class="fas fa-check me-1"></i>Select Current Folder
                           </button>` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Store current browse context
    window.currentBrowseContext = { browseType, targetType };

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    // Load initial directory (show all drives)
    loadBrowserDirectory('drives');
}

function browseFinalPath() {
    // Legacy function - redirect to browseDestinationFolder
    browseDestinationFolder();
}

// EXACT COPY FROM CROSSCHECKER.JS - Browser navigation functions
async function loadBrowserDirectory(path) {
    try {
        const response = await fetch('/api/browse_files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                path: path,
                type: window.currentBrowseContext.browseType === 'file' ? 'video' : 'all'
            })
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('currentBrowserPath').value = data.path;
            displayBrowserItems(data.items);
        } else {
            showBrowserError(`Error: ${data.error}`);
        }
    } catch (error) {
        showBrowserError(`Network error: ${error.message}`);
    }
}

function displayBrowserItems(items) {
    const container = document.getElementById('fileBrowserContent');

    if (items.length === 0) {
        container.innerHTML = '<div class="text-muted text-center p-3">No items found</div>';
        return;
    }

    let html = '<div class="list-group list-group-flush">';

    items.forEach(item => {
        const isFolder = item.type === 'folder';
        const isDrive = item.type === 'drive';
        const isVideo = !isFolder && !isDrive && item.name.toLowerCase().match(/\.(mov|mp4|avi|mkv|wmv)$/);

        let icon, textClass, clickAction;

        if (isDrive) {
            icon = 'fas fa-hdd text-primary';
            textClass = 'text-dark fw-bold';
            clickAction = `loadBrowserDirectory('${item.path.replace(/\\/g, '\\\\')}')`;
        } else if (isFolder) {
            icon = 'fas fa-folder text-warning';
            textClass = 'text-dark';
            clickAction = `loadBrowserDirectory('${item.path.replace(/\\/g, '\\\\')}')`;
        } else if (isVideo) {
            icon = 'fas fa-file-video text-info';
            textClass = 'text-dark';
            clickAction = window.currentBrowseContext.browseType === 'file' ?
                         `selectBrowserFile('${item.path.replace(/\\/g, '\\\\')}')` : '';
        } else {
            icon = 'fas fa-file text-muted';
            textClass = 'text-muted';
            clickAction = '';
        }

        const clickable = clickAction ? 'list-group-item-action' : '';
        const cursor = clickAction ? 'cursor-pointer' : '';

        html += `
            <div class="list-group-item ${clickable} d-flex justify-content-between align-items-center ${cursor}"
                 ${clickAction ? `onclick="${clickAction}"` : ''}>
                <div class="${textClass}">
                    <i class="${icon} me-2"></i>
                    ${item.name}
                </div>
                <small class="text-muted">
                    ${isDrive ? 'Drive' : isFolder ? 'Folder' : formatFileSize(item.size)}
                </small>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

function selectBrowserFile(filePath) {
    // This function is for video file selection (not used in editor folder browsing)
    console.log('File selected:', filePath);
}

function selectCurrentFolder() {
    const currentPath = document.getElementById('currentBrowserPath').value;
    const { targetType } = window.currentBrowseContext;

    if (!currentPath || currentPath === 'Computer') {
        showAlert('error', 'Please navigate to a specific folder first.');
        return;
    }

    // Set the selected path based on context
    if (targetType === 'source') {
        document.getElementById('sourceFolder').value = currentPath;
        document.getElementById('sourceFolder').classList.add('is-valid');
        showAlert('success', 'Source folder selected successfully!');
    } else if (targetType === 'destination') {
        document.getElementById('destinationFolder').value = currentPath;
        document.getElementById('destinationFolder').classList.add('is-valid');
        showAlert('success', 'Destination folder selected successfully!');
    }

    // Trigger form validation
    validateForm();

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('serverFileBrowserModal'));
    modal.hide();
}

function navigateUp() {
    const currentPath = document.getElementById('currentBrowserPath').value;
    if (currentPath && currentPath !== 'Computer') {
        const parentPath = currentPath.substring(0, currentPath.lastIndexOf('\\'));
        if (parentPath && parentPath.length > 2) { // Not a drive root
            loadBrowserDirectory(parentPath);
        } else {
            loadBrowserDirectory('drives'); // Go back to drives view
        }
    }
}

function refreshBrowser() {
    const currentPath = document.getElementById('currentBrowserPath').value;
    if (currentPath === 'Computer') {
        loadBrowserDirectory('drives');
    } else {
        loadBrowserDirectory(currentPath);
    }
}

function showBrowserError(message) {
    const container = document.getElementById('fileBrowserContent');
    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function submitCompleteEditing() {
    // Get form data
    const sourceFolder = document.getElementById('sourceFolder').value.trim();
    const destinationFolder = document.getElementById('destinationFolder').value.trim();
    const editingNotes = document.getElementById('editingNotes').value.trim();
    const adminReviewNotes = document.getElementById('adminReviewNotes').value.trim();
    const requestAdminApproval = document.getElementById('requestAdminApproval').checked;

    // Validate required fields
    if (!sourceFolder) {
        showAlert('error', 'Please select a source folder.');
        return;
    }

    if (!destinationFolder) {
        showAlert('error', 'Please select a destination folder.');
        return;
    }

    if (!editingNotes) {
        showAlert('error', 'Please provide editing notes.');
        return;
    }

    // Confirm submission
    const confirmMessage = `Complete editing and move folder?\n\nSource: ${sourceFolder}\nDestination: ${destinationFolder}\nAdmin Review: ${requestAdminApproval ? 'Requested' : 'Not requested'}`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    const button = document.getElementById('confirmCompleteBtn');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    button.disabled = true;
    button.classList.add('loading');

    // Prepare request data
    const requestData = {
        source_folder: sourceFolder,
        destination_folder: destinationFolder,
        editing_notes: editingNotes,
        admin_review_notes: adminReviewNotes,
        request_admin_approval: requestAdminApproval
    };

    fetch(`/api/complete_editing/${currentEditingId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Editing completed successfully! Folder moved and admin approval requested.');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('completeEditingModal'));
            modal.hide();

            // Show success details
            if (data.folder_moved) {
                showAlert('info', `Folder successfully moved from "${sourceFolder}" to "${destinationFolder}"`);
            }

            if (data.admin_approval_requested) {
                showAlert('info', 'Admin approval request has been recorded for review.');
            }

            // Refresh page
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showAlert('error', data.error || 'Error completing editing.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Network error while completing editing.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
        button.classList.remove('loading');
    });
}

function refreshStatistics() {
    // This would refresh the dashboard statistics
    console.log('Refreshing editor statistics...');
}

function showAlert(type, message) {
    // Create and show alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at the top of the main container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
