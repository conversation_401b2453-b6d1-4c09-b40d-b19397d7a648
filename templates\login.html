{% extends "base.html" %}

{% block title %}Login - Archives MediaFlow Pro{% endblock %}

{% block content %}
<div class="login-wrapper">
    <div class="login-background">
        <div class="animated-bg"></div>
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
    </div>

    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center login-brand-side">
                <div class="text-center text-white">
                    <div class="brand-logo mb-4">
                        <i class="fas fa-play-circle fa-5x text-gradient"></i>
                    </div>
                    <h1 class="display-4 fw-bold mb-3 text-gradient">Archives MediaFlow Pro</h1>
                    <p class="lead mb-4 opacity-90">Professional Archives Content Management System</p>
                    <div class="feature-list">
                        <div class="feature-item">
                            <i class="fas fa-check-circle me-2"></i>
                            Advanced Media Processing
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle me-2"></i>
                            Intelligent Workflow Management
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle me-2"></i>
                            Real-time Collaboration
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center login-form-side">
                <div class="login-form-container">
                    <div class="text-center mb-5">
                        <div class="d-lg-none mb-3">
                            <i class="fas fa-play-circle fa-3x text-primary"></i>
                        </div>
                        <h2 class="fw-bold text-dark mb-2">Welcome Back</h2>
                        <p class="text-muted">Sign in to your Archives MediaFlow Pro account</p>
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-4">
                            <label for="username" class="form-label fw-semibold text-dark">
                                <i class="fas fa-user me-2 text-primary"></i>Username
                            </label>
                            <div class="input-group input-group-lg">
                                <input type="text" class="form-control form-control-modern"
                                       id="username" name="username" required
                                       placeholder="Enter your username">
                                <div class="invalid-feedback">
                                    Please provide a valid username.
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label fw-semibold text-dark">
                                <i class="fas fa-lock me-2 text-primary"></i>Password
                            </label>
                            <div class="input-group input-group-lg">
                                <input type="password" class="form-control form-control-modern"
                                       id="password" name="password" required
                                       placeholder="Enter your password">
                                <div class="invalid-feedback">
                                    Please provide a valid password.
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg w-100 btn-modern mb-4">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In to Archives MediaFlow Pro
                        </button>
                    </form>

                    <div class="roles-info">
                        <h6 class="text-center text-muted mb-3">
                            <i class="fas fa-users me-2"></i>System Roles
                        </h6>
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="role-badge executor">
                                    <i class="fas fa-cogs me-1"></i>Executor
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="role-badge crosschecker">
                                    <i class="fas fa-check-double me-1"></i>Cross-Checker
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="role-badge editor">
                                    <i class="fas fa-edit me-1"></i>Editor
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="role-badge admin">
                                    <i class="fas fa-shield-alt me-1"></i>Admin
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                Demo Password: <code class="bg-light px-2 py-1 rounded">password123</code>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.login-wrapper {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    z-index: -1;
}

.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.floating-element {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float-up 15s infinite linear;
}

.floating-element:nth-child(1) {
    width: 80px;
    height: 80px;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    width: 60px;
    height: 60px;
    left: 20%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    width: 100px;
    height: 100px;
    left: 35%;
    animation-delay: 4s;
}

.floating-element:nth-child(4) {
    width: 40px;
    height: 40px;
    left: 70%;
    animation-delay: 6s;
}

.floating-element:nth-child(5) {
    width: 120px;
    height: 120px;
    left: 85%;
    animation-delay: 8s;
}

@keyframes float-up {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.login-brand-side {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.login-form-side {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
}

.text-gradient {
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-logo {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.feature-list {
    text-align: left;
    display: inline-block;
}

.feature-item {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    opacity: 0.9;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
}

.form-control-modern {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control-modern:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: rgba(255, 255, 255, 1);
}

.btn-modern {
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-modern:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover:before {
    left: 100%;
}

.roles-info {
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.role-badge {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.role-badge.executor {
    background: linear-gradient(45deg, #4e73df, #36b9cc);
    color: white;
}

.role-badge.crosschecker {
    background: linear-gradient(45deg, #1cc88a, #17a2b8);
    color: white;
}

.role-badge.editor {
    background: linear-gradient(45deg, #f6c23e, #fd7e14);
    color: white;
}

.role-badge.admin {
    background: linear-gradient(45deg, #e74a3b, #dc3545);
    color: white;
}

.role-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

@media (max-width: 991.98px) {
    .login-form-side {
        background: rgba(255, 255, 255, 0.95);
    }

    .login-form-container {
        padding: 1rem;
    }
}
</style>
{% endblock %}
