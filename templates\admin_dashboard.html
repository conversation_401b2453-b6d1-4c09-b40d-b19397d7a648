{% extends "base.html" %}

{% block title %}Admin Dashboard - Archives MediaFlow Pro{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-shield-alt me-2 text-danger"></i>Admin Control Center
                    </h1>
                    <p class="text-muted mb-0">System Administration & Performance Analytics</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-danger fs-6 px-3 py-2">{{ session.role }}</span>
                    <div class="mt-2">
                        <small class="text-muted">Last login: {{ session.get('last_login', 'N/A') }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Summary Panel -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Processed
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_processed }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Validated
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.validated_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Editor Pending
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.editor_pending }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Editor Completed
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.editor_completed }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-award fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% for executor_stat in stats.executor_stats %}
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                {{ executor_stat[0] }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ executor_stat[1] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-cog fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Navigation Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-tabs" id="adminTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                        <i class="fas fa-chart-line me-2"></i>Performance Overview
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                        <i class="fas fa-stream me-2"></i>Live Activity
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                        <i class="fas fa-users me-2"></i>User Management
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="records-tab" data-bs-toggle="tab" data-bs-target="#records" type="button" role="tab">
                        <i class="fas fa-database me-2"></i>All Records
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="adminTabContent">
        <!-- Performance Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="row">
                <!-- Daily Processing Chart -->
                <div class="col-xl-6 col-lg-12 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-bar me-2"></i>Daily Processing by Executors
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyProcessingChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Cross-Checker Activity Chart -->
                <div class="col-xl-6 col-lg-12 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">
                                <i class="fas fa-chart-line me-2"></i>Cross-Checker Activity Trends
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="crosscheckerChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Editor Progress Chart -->
                <div class="col-xl-6 col-lg-12 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">
                                <i class="fas fa-chart-area me-2"></i>Editor Progress Status
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="editorChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Workflow Status Pie Chart -->
                <div class="col-xl-6 col-lg-12 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">
                                <i class="fas fa-chart-pie me-2"></i>Overall Workflow Status
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="workflowStatusChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Activity Tab -->
        <div class="tab-pane fade" id="activity" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-stream me-2"></i>Real-time Activity Feed
                            </h6>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshActivity()">
                                <i class="fas fa-sync-alt me-1"></i>Refresh
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="activityFeed">
                                {% if recent_activity %}
                                    {% for activity in recent_activity %}
                                    <div class="activity-item border-bottom pb-3 mb-3">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <span class="badge bg-primary me-2">{{ activity[3] }}</span>
                                                    {{ activity[0] }}
                                                </h6>
                                                <p class="text-muted mb-0">{{ activity[1] }}</p>
                                            </div>
                                            <small class="text-muted">{{ activity[2] }}</small>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No recent activity</h5>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Management Tab -->
        <div class="tab-pane fade" id="users" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-users me-2"></i>User Management
                            </h6>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <i class="fas fa-plus me-2"></i>Add New User
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="usersTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Username</th>
                                            <th>Role</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for user in all_users %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-user-circle text-primary me-2"></i>
                                                    <strong>{{ user[1] }}</strong>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-{% if user[2] == 'Admin' %}danger{% elif user[2] == 'Cross-Checker' %}success{% elif user[2] == 'Editor' %}warning{% else %}primary{% endif %}">
                                                    {{ user[2] }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>{{ user[3] }}</small>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1"
                                                        onclick="editUser({{ user[0] }}, '{{ user[1] }}', '{{ user[2] }}')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                {% if user[0] != session.user_id %}
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteUser({{ user[0] }}, '{{ user[1] }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- All Records Tab -->
        <div class="tab-pane fade" id="records" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-database me-2"></i>All Metadata Records
                            </h6>
                            <div>
                                <button class="btn btn-outline-success me-2" onclick="exportRecords()">
                                    <i class="fas fa-download me-1"></i>Export CSV
                                </button>
                                <button class="btn btn-outline-primary" onclick="loadAllRecords()">
                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="recordsTable">
                                <div class="text-center py-4">
                                    <button class="btn btn-primary" onclick="loadAllRecords()">
                                        <i class="fas fa-database me-2"></i>Load All Records
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit User Modal -->
<div class="modal fade" id="userModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="userModalTitle">
                    <i class="fas fa-user-plus me-2"></i>Add New User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" id="userId" value="">

                    <div class="mb-3">
                        <label for="username" class="form-label fw-bold">Username</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>

                    <div class="mb-3">
                        <label for="role" class="form-label fw-bold">Role</label>
                        <select class="form-select" id="role" required>
                            <option value="">Select Role</option>
                            <option value="Executor 1">Executor 1</option>
                            <option value="Executor 2">Executor 2</option>
                            <option value="Executor 3">Executor 3</option>
                            <option value="Cross-Checker">Cross-Checker</option>
                            <option value="Editor">Editor</option>
                            <option value="Admin">Admin</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label fw-bold">Password</label>
                        <input type="password" class="form-control" id="password">
                        <div class="form-text" id="passwordHelp">Leave blank to keep current password (for editing)</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">
                    <i class="fas fa-save me-2"></i>Save User
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-secondary {
    border-left: 0.25rem solid #858796 !important;
}
.text-xs {
    font-size: 0.7rem;
}
.activity-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='admin.js') }}"></script>
{% endblock %}
