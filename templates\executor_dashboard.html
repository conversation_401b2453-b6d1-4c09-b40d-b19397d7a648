{% extends "base.html" %}

{% block title %}Executor Dashboard - Archives System Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='tree-view.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-cogs me-2 text-primary"></i>Executor Dashboard
                    </h1>
                    <p class="text-muted mb-0">Content Processing & Metadata Management</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary fs-6 px-3 py-2">{{ session.role }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Pending Files
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Completed Yesterday
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ yesterday_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Daily Average (7 days)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ daily_average }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Processed
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_processed }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tasks fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Verified by Crosschecker
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ verified_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-double fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Rejected by Crosschecker
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ rejected_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="mainTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="folder-access-tab" data-bs-toggle="tab" 
                                    data-bs-target="#folder-access" type="button" role="tab">
                                <i class="fas fa-folder-open me-2"></i>Folder Access
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="processing-queue-tab" data-bs-toggle="tab" 
                                    data-bs-target="#processing-queue" type="button" role="tab">
                                <i class="fas fa-list me-2"></i>Processing Queue
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="mainTabsContent">
                        <!-- Folder Access Tab -->
                        <div class="tab-pane fade show active" id="folder-access" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body text-center py-5">
                                            <i class="fas fa-folder-plus fa-3x text-primary mb-3"></i>
                                            <h5 class="card-title">Select Folder to Process</h5>
                                            <p class="card-text text-muted">
                                                Browse and select a folder containing content files for processing.
                                            </p>
                                            <button type="button" class="btn btn-primary btn-lg" id="browseFolder">
                                                <i class="fas fa-search me-2"></i>Browse Folders
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div id="folderTreeContainer" class="d-none">
                                        <!-- Folder Statistics -->
                                        <div id="folderStats" class="folder-stats mb-4">
                                            <!-- Statistics cards will be populated here -->
                                        </div>

                                        <!-- Folder Tree Header -->
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">
                                                <i class="fas fa-sitemap me-2 text-primary"></i>Interactive Folder Structure
                                            </h6>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-secondary" id="expandAllBtn" title="Expand All">
                                                    <i class="fas fa-expand-arrows-alt"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" id="collapseAllBtn" title="Collapse All">
                                                    <i class="fas fa-compress-arrows-alt"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Tree View Container -->
                                        <div id="folderTree" class="tree-container">
                                            <!-- Interactive folder tree will be populated here -->
                                        </div>

                                        <!-- Selection Info -->
                                        <div id="selectionInfo" class="selection-info">
                                            <!-- Selection details will be shown here -->
                                        </div>

                                        <!-- Process Button -->
                                        <div class="mt-3 text-center">
                                            <button type="button" class="btn btn-success btn-lg" id="processFolder" disabled>
                                                <i class="fas fa-play me-2"></i>Process Selected Folder
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Processing Queue Tab -->
                        <div class="tab-pane fade" id="processing-queue" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Folder Name</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="queueTableBody">
                                        <tr>
                                            <td colspan="4" class="text-center text-muted py-4">
                                                <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                                No items in processing queue
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Metadata Modal -->
<div class="modal fade" id="metadataModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Content Metadata Form
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="metadataForm">
                    <!-- Form content will be loaded here -->
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.text-xs {
    font-size: 0.7rem;
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='executor.js') }}"></script>
<script>
// Auto-refresh dashboard metrics every 30 seconds
function refreshDashboardMetrics() {
    fetch('/api/executor/metrics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the metric cards
                document.querySelector('.text-primary + .h5').textContent = data.pending_count;
                document.querySelector('.text-success + .h5').textContent = data.yesterday_count;
                document.querySelector('.text-info + .h5').textContent = data.daily_average;

                // Update additional metrics if they exist
                const additionalMetrics = document.querySelectorAll('.row:nth-child(3) .h5');
                if (additionalMetrics.length >= 3) {
                    additionalMetrics[0].textContent = data.total_processed;
                    additionalMetrics[1].textContent = data.verified_count;
                    additionalMetrics[2].textContent = data.rejected_count;
                }

                console.log('📊 Dashboard metrics refreshed');
            }
        })
        .catch(error => {
            console.error('❌ Error refreshing metrics:', error);
        });
}

// Refresh metrics on page load and every 30 seconds
document.addEventListener('DOMContentLoaded', function() {
    // Initial refresh after 2 seconds
    setTimeout(refreshDashboardMetrics, 2000);

    // Set up periodic refresh every 30 seconds
    setInterval(refreshDashboardMetrics, 30000);

    console.log('📊 Dashboard auto-refresh enabled (30 seconds)');
});
</script>
{% endblock %}
