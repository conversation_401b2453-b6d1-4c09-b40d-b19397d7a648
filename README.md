# MediaFlow Pro - Content Management System

**Professional Flask-based Content Management and Processing System**

A comprehensive web application for managing content metadata, processing workflows, and automated content organization with role-based access control and Google Sheets integration.

## 🌟 Features

### Core Functionality
- **Role-Based Access Control**: Executor, Cross-Checker, Editor, and Admin roles
- **Automated Workflows**: Streamlined processing from upload to final delivery
- **Google Sheets Integration**: Automatic metadata population and logging
- **Audio Extraction**: FFMPEG-powered audio extraction from video files
- **File Management**: Intelligent folder organization and renaming
- **Real-Time Monitoring**: Live activity feeds and progress tracking

### Enterprise Features
- **Comprehensive Admin Dashboard**: User management, system monitoring, audit trails
- **Security**: Session management, role-based permissions, secure authentication
- **Scalability**: SQLite database with migration-ready structure
- **Logging**: Comprehensive system and user activity logging
- **Testing**: Full test suite with production readiness validation

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- FFMPEG (for audio processing)
- Google Sheets API credentials (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd archives-mediaflow-pro
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Initialize database**
   ```bash
   python -c "import app; app.init_database()"
   ```

6. **Run the application**
   ```bash
   python app.py
   ```

7. **Access the application**
   - Open http://localhost:5005
   - Login with default admin credentials: admin/password123

## 👥 User Roles

### Executor (Executor 1 & Executor 2)
- Upload and process media files
- Input metadata and project details
- Submit items for cross-checking
- Monitor processing status

### Cross-Checker
- Review and validate submitted items
- Approve/reject with detailed notes
- Trigger audio extraction and folder management
- Queue items for editing when needed

### Editor
- Access editing queue
- Process approved items
- Manage final delivery folders
- Complete editing workflows

### Admin
- Full system access and monitoring
- User management (create, edit, delete users)
- System configuration and maintenance
- Comprehensive reporting and analytics

## 🔧 Configuration

### Environment Variables
Key configuration options in `.env`:

```env
# Flask Configuration
SECRET_KEY=your-secret-key
FLASK_ENV=production

# Google Sheets
GOOGLE_SHEET_SEARCH_ID=your-search-sheet-id
GOOGLE_SHEET_UPLOAD_ID=your-upload-sheet-id
GOOGLE_CREDENTIALS_FILE=credentials.json

# Server
HOST=0.0.0.0
PORT=5005
```

### Google Sheets Setup
1. Create Google Cloud Project
2. Enable Google Sheets API
3. Create service account and download credentials
4. Share your Google Sheets with the service account email
5. Update sheet IDs in `.env`

## 🧪 Testing

### Run Production Tests
```bash
python production_ready_tests.py
```

### Run Comprehensive Test Suite
```bash
python comprehensive_testing_suite.py
```

### Test Individual Components
```bash
python test_complete_archives_mediaflow_pro.py
```

## 📁 Project Structure

```
archives-mediaflow-pro/
├── app.py                          # Main Flask application
├── requirements.txt                # Python dependencies
├── .env.example                   # Environment configuration template
├── .gitignore                     # Git ignore rules
├── README.md                      # This file
├── archives_mediaflow_pro.db      # SQLite database (created on first run)
├── templates/                     # HTML templates
│   ├── base.html                 # Base template
│   ├── login.html                # Login page
│   ├── dashboard.html            # Main dashboard
│   ├── executor.html             # Executor dashboard
│   ├── crosschecker.html         # Cross-checker dashboard
│   ├── editor.html               # Editor dashboard
│   └── admin.html                # Admin dashboard
├── static/                       # Static assets
│   ├── style.css                 # Main stylesheet
│   └── app.js                    # JavaScript functionality
└── tests/                        # Test files
    ├── production_ready_tests.py
    ├── comprehensive_testing_suite.py
    └── test_complete_archives_mediaflow_pro.py
```

## 🔒 Security Features

- **Session Management**: Secure session handling with timeouts
- **Role-Based Access**: Strict permission controls for all endpoints
- **Password Security**: Hashed passwords using Werkzeug
- **Input Validation**: Comprehensive input sanitization
- **CSRF Protection**: Built-in Flask security features
- **Audit Logging**: Complete activity tracking

## 🚀 Production Deployment

### Docker Deployment (Recommended)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5005
CMD ["python", "app.py"]
```

### Traditional Server Deployment
1. Set up Python environment on server
2. Install dependencies and configure environment
3. Set up reverse proxy (nginx/Apache)
4. Configure SSL certificates
5. Set up process manager (systemd/supervisor)

### Environment-Specific Configuration
- **Development**: Set `FLASK_ENV=development` and `FLASK_DEBUG=True`
- **Production**: Set `FLASK_ENV=production` and `FLASK_DEBUG=False`
- **Testing**: Use separate database and disable external integrations

## 📊 Monitoring and Maintenance

### Health Checks
- Application status: `GET /health`
- Database connectivity: Built-in monitoring
- Google Sheets integration: Automatic validation

### Logging
- Application logs: `archives_mediaflow_pro.log`
- System logs: Database table `system_logs`
- User activity: Comprehensive audit trail

### Backup Strategy
- Database: Regular SQLite backups
- Media files: Separate backup solution
- Configuration: Version control

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review test files for usage examples

## 🏆 Production Ready

Archives MediaFlow Pro has been thoroughly tested and validated for enterprise deployment:
- ✅ All user roles and workflows tested
- ✅ Database integrity verified
- ✅ Security features validated
- ✅ API endpoints tested
- ✅ Error handling comprehensive
- ✅ Performance optimized

**Ready for immediate production deployment!**
