/* Archives System Management - Professional Styles */

:root {
    --primary-color: #4e73df;
    --primary-dark: #3c5dcd;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --white: #ffffff;
    --gray-100: #f8f9fc;
    --gray-200: #eaecf4;
    --gray-300: #dddfeb;
    --gray-400: #d1d3e2;
    --gray-500: #b7b9cc;
    --gray-600: #858796;
    --gray-700: #6e707e;
    --gray-800: #5a5c69;
    --gray-900: #3a3b45;
    --border-radius: 0.35rem;
    --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    --transition: all 0.3s ease-in-out;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--light-color);
    color: var(--gray-800);
    line-height: 1.6;
    font-size: 0.875rem;
}

/* Navigation Styles */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--white) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Card Styles */
.card {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background-color: var(--white);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: var(--gray-100);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
}

.card-body {
    padding: 1.25rem;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border-width: 1px;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--gray-800);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

/* Form Styles */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    transition: var(--transition);
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

/* Soft Renamed Field Styles */
#softRenamed {
    position: relative;
}

#softRenamed.border-success {
    border-color: var(--success-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(28, 200, 138, 0.25) !important;
    background-color: #f8fff9 !important;
}

#softRenamed:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.soft-renamed-editable {
    background-color: #f8fff9 !important;
    border-left: 4px solid var(--success-color) !important;
}

/* Table Styles */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--gray-800);
    color: var(--white);
    border: none;
    font-weight: 600;
    padding: 0.75rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table tbody td {
    padding: 0.75rem;
    border-top: 1px solid var(--gray-200);
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: var(--gray-100);
}

/* Alert Styles */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

/* Modal Styles */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid var(--gray-200);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
}

/* Tab Styles */
.nav-tabs {
    border-bottom: 1px solid var(--gray-200);
}

.nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    color: var(--gray-600);
    font-weight: 500;
    padding: 0.75rem 1rem;
}

.nav-tabs .nav-link.active {
    background-color: var(--white);
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

/* Utility Classes */
.text-xs {
    font-size: 0.7rem !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-uppercase {
    text-transform: uppercase !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.rounded-3 {
    border-radius: 0.5rem !important;
}

.rounded-4 {
    border-radius: 0.75rem !important;
}

/* Border Left Utilities */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.border-left-danger {
    border-left: 0.25rem solid var(--danger-color) !important;
}

/* Custom Components */
.folder-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1rem;
    background-color: var(--white);
}

.folder-item {
    padding: 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: var(--transition);
    margin: 0.25rem 0;
}

.folder-item:hover {
    background-color: var(--gray-100);
}

.folder-item.selected {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Status Indicators */
.status-pending {
    background-color: var(--warning-color);
    color: var(--gray-800);
}

.status-processing {
    background-color: var(--info-color);
    color: var(--white);
}

.status-completed {
    background-color: var(--success-color);
    color: var(--white);
}

.status-failed {
    background-color: var(--danger-color);
    color: var(--white);
}
