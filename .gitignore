# Archives MediaFlow Pro - Git Ignore File

# Environment and Configuration
.env
.env.local
.env.production
.env.development
config.py

# Database Files
*.db
*.sqlite
*.sqlite3
archives_mediaflow_pro.db

# Google Credentials
credentials.json
service-account-key.json
google-credentials.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
archives_mediaflow_pro.log*

# Temporary Files
tmp/
temp/
.tmp/
.temp/

# Upload and Processing Directories
uploads/
processed/
audio_output/
final_folders/
editor_final/

# Test Files
test_data/
production_test/
*_test/
test_*/

# Backup Files
*.bak
*.backup
backup_*/

# Media Files (for testing)
*.mov
*.mp4
*.avi
*.wav
*.mp3

# Documentation Build
docs/_build/
site/

# Coverage Reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Flask
instance/
.webassets-cache

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
