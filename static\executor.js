// Executor Dashboard JavaScript
let selectedFolder = null;
let googleSheetsClient = null;

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeExecutorDashboard();
});

function initializeExecutorDashboard() {
    // Initialize event listeners
    document.getElementById('browseFolder').addEventListener('click', openFolderBrowser);
    document.getElementById('processFolder').addEventListener('click', openMetadataForm);

    // Add expand/collapse all buttons event listeners
    const expandAllBtn = document.getElementById('expandAllBtn');
    const collapseAllBtn = document.getElementById('collapseAllBtn');

    if (expandAllBtn) {
        expandAllBtn.addEventListener('click', expandAllNodes);
    }

    if (collapseAllBtn) {
        collapseAllBtn.addEventListener('click', collapseAllNodes);
    }

    // Load processing queue
    loadProcessingQueue();

    // Refresh statistics every 30 seconds
    setInterval(refreshStatistics, 30000);
}

function openFolderBrowser() {
    console.log('📁 Opening server-side folder browser (restricted to E:\\Work space)...');
    showExecutorFolderBrowserModal();
}

function showExecutorFolderBrowserModal() {
    const modalId = 'executorFolderBrowserModal';

    // Remove existing modal if any
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const modalHTML = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-folder me-2"></i>
                            Select Folder from E:\\Work space
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-folder"></i>
                                </span>
                                <input type="text" class="form-control" id="currentExecutorBrowserPath" value="E:\\Work space" readonly>
                                <button class="btn btn-outline-secondary" onclick="navigateExecutorUp()" title="Go up">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                                <button class="btn btn-outline-primary" onclick="refreshExecutorBrowser()" title="Refresh">
                                    <i class="fas fa-sync"></i>
                                </button>
                            </div>
                        </div>
                        <div id="executorFolderBrowserContent" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 10px;">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="mt-2">Loading directory...</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Navigate to the desired folder and click "Select Current Folder". Access is restricted to E:\\Work space only.
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="selectExecutorCurrentFolder()">
                            <i class="fas fa-check me-1"></i>Select Current Folder
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    // Load initial directory (start at E:\Work space)
    loadExecutorBrowserDirectory('E:\\Work space');
}

async function loadExecutorBrowserDirectory(path) {
    try {
        // Ensure we always start from E:\Work space (restricted for executors)
        if (!path || path === '/' || path === '' || path === 'drives') {
            path = 'E:\\Work space';
        }

        // Security: Ensure path stays within E:\Work space
        if (!path.toUpperCase().startsWith('E:\\WORK SPACE')) {
            console.warn('🔒 Path outside E:\\Work space detected, redirecting to workspace:', path);
            path = 'E:\\Work space';
        }

        const response = await fetch('/api/browse_files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                path: path,
                type: 'all'  // Show all folders and files
            })
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('currentExecutorBrowserPath').value = data.path;
            displayExecutorBrowserItems(data.items);
        } else {
            showExecutorBrowserError(`Error: ${data.error}`);
        }
    } catch (error) {
        showExecutorBrowserError(`Network error: ${error.message}`);
    }
}

function displayExecutorBrowserItems(items) {
    const container = document.getElementById('executorFolderBrowserContent');

    if (items.length === 0) {
        container.innerHTML = '<div class="text-muted text-center p-3">No items found</div>';
        return;
    }

    let html = '<div class="list-group list-group-flush">';

    items.forEach(item => {
        const icon = item.type === 'folder' ? 'fa-folder' : 'fa-file';
        const color = item.type === 'folder' ? 'text-warning' : 'text-muted';
        const clickAction = item.type === 'folder' ? `onclick="loadExecutorBrowserDirectory('${item.path.replace(/\\/g, '\\\\')}')"` : '';
        const cursor = item.type === 'folder' ? 'cursor-pointer' : '';

        html += `
            <div class="list-group-item list-group-item-action d-flex align-items-center ${cursor}" ${clickAction}>
                <i class="fas ${icon} ${color} me-3"></i>
                <div class="flex-grow-1">
                    <div class="fw-medium">${item.name}</div>
                    ${item.type === 'folder' ? '<small class="text-muted">Click to open</small>' : `<small class="text-muted">${formatFileSize(item.size)}</small>`}
                </div>
                ${item.type === 'folder' ? '<i class="fas fa-chevron-right text-muted"></i>' : ''}
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

function navigateExecutorUp() {
    const currentPath = document.getElementById('currentExecutorBrowserPath').value;

    // Don't allow navigation above E:\Work space
    if (currentPath.toUpperCase() === 'E:\\WORK SPACE') {
        console.log('🔒 Already at workspace root, cannot navigate up');
        return;
    }

    const parentPath = currentPath.substring(0, currentPath.lastIndexOf('\\'));

    // Ensure we don't go above E:\Work space
    if (!parentPath.toUpperCase().startsWith('E:\\WORK SPACE')) {
        console.log('🔒 Cannot navigate above E:\\Work space');
        return;
    }

    loadExecutorBrowserDirectory(parentPath);
}

function refreshExecutorBrowser() {
    const currentPath = document.getElementById('currentExecutorBrowserPath').value;
    loadExecutorBrowserDirectory(currentPath);
}

function selectExecutorCurrentFolder() {
    const currentPath = document.getElementById('currentExecutorBrowserPath').value;
    console.log(`📁 Selected folder: ${currentPath}`);

    // Close the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('executorFolderBrowserModal'));
    modal.hide();

    // Process the selected folder
    processSelectedServerFolder(currentPath);
}

function showExecutorBrowserError(message) {
    const container = document.getElementById('executorFolderBrowserContent');
    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

async function processSelectedServerFolder(folderPath) {
    try {
        console.log(`📁 Processing server folder: ${folderPath}`);

        // Show loading state
        const container = document.getElementById('folderTreeContainer');
        const tree = document.getElementById('folderTree');

        tree.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mt-2">Loading folder structure...</div>
            </div>
        `;
        container.classList.remove('d-none');

        // Get folder contents from server
        const response = await fetch('/api/browse_files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                path: folderPath,
                type: 'all'
            })
        });

        const data = await response.json();

        if (data.success) {
            // Build folder structure for display
            const folderName = folderPath.split('\\').pop() || folderPath;
            displayServerFolderStructure(data.items, folderName, folderPath);

            // Enable process button
            selectedFolder = {
                name: folderName,
                path: folderPath,
                serverPath: true  // Flag to indicate this is a server path
            };

            document.getElementById('processFolder').disabled = false;

            // Show folder statistics
            displayServerFolderStatistics(data.items, folderName);
        } else {
            throw new Error(data.error);
        }

    } catch (error) {
        console.error('❌ Error processing server folder:', error);
        showAlert('error', `Error processing folder: ${error.message}`);

        // Clear the tree
        document.getElementById('folderTree').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Error loading folder: ${error.message}
            </div>
        `;
    }
}

function displayFolderStructure(files, folderName) {
    const container = document.getElementById('folderTreeContainer');
    const tree = document.getElementById('folderTree');

    // Clear previous content
    tree.innerHTML = '';

    // Create folder structure
    const folderStructure = buildFolderStructure(files);
    const treeHTML = renderInteractiveTree(folderStructure, folderName);

    tree.innerHTML = treeHTML;
    container.classList.remove('d-none');

    // Enable process button
    selectedFolder = {
        name: folderName,
        files: files,
        path: files[0].webkitRelativePath.split('/')[0]
    };

    document.getElementById('processFolder').disabled = false;

    // Initialize tree interactions
    initializeTreeInteractions();

    // Show folder statistics
    displayFolderStatistics(files, folderName);
}

function buildFolderStructure(files) {
    const structure = {};
    
    Array.from(files).forEach(file => {
        const pathParts = file.webkitRelativePath.split('/');
        let current = structure;
        
        pathParts.forEach((part, index) => {
            if (!current[part]) {
                current[part] = index === pathParts.length - 1 ? 'file' : {};
            }
            if (index < pathParts.length - 1) {
                current = current[part];
            }
        });
    });
    
    return structure;
}

function renderInteractiveTree(structure, name, level = 0, path = '') {
    let html = '';
    const currentPath = path ? `${path}/${name}` : name;

    if (level === 0) {
        // Root folder - always expanded
        html += `
            <div class="tree-node tree-root" data-path="${currentPath}">
                <div class="tree-item folder-item selected" data-level="${level}">
                    <span class="tree-icon">
                        <i class="fas fa-folder-open text-warning"></i>
                    </span>
                    <span class="tree-label">
                        <strong>${name}</strong>
                    </span>
                    <span class="tree-badge">
                        <span class="badge bg-primary ms-2">${countItems(structure).folders} folders</span>
                        <span class="badge bg-info ms-1">${countItems(structure).files} files</span>
                    </span>
                </div>
                <div class="tree-children" id="root-children">
                    ${renderTreeChildren(structure, level + 1, currentPath)}
                </div>
            </div>
        `;
    }

    return html;
}

function renderTreeChildren(structure, level, parentPath) {
    let html = '';
    let nodeCounter = 0;

    const sortedKeys = Object.keys(structure).sort((a, b) => {
        // Folders first, then files
        const aIsFolder = structure[a] !== 'file';
        const bIsFolder = structure[b] !== 'file';

        if (aIsFolder && !bIsFolder) return -1;
        if (!aIsFolder && bIsFolder) return 1;
        return a.localeCompare(b);
    });

    sortedKeys.forEach((key, index) => {
        const currentPath = `${parentPath}/${key}`;

        if (structure[key] === 'file') {
            // File node
            const fileExt = key.split('.').pop().toLowerCase();
            const fileIcon = getFileIcon(fileExt);

            html += `
                <div class="tree-node tree-file" data-path="${currentPath}">
                    <div class="tree-item file-item" data-level="${level}" style="margin-left: ${level * 20}px;">
                        <span class="tree-icon">
                            <i class="${fileIcon}"></i>
                        </span>
                        <span class="tree-label">${key}</span>
                    </div>
                </div>
            `;
        } else {
            // Folder node with children
            const hasChildren = Object.keys(structure[key]).length > 0;
            const nodeId = `folder-${level}-${index}-${Date.now()}`;

            html += `
                <div class="tree-node tree-folder" data-path="${currentPath}">
                    <div class="tree-item folder-item" data-level="${level}" style="margin-left: ${level * 20}px;">
                        ${hasChildren ? `
                            <span class="tree-toggle" data-target="${nodeId}" onclick="toggleFolder('${nodeId}')">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        ` : ''}
                        <span class="tree-icon">
                            <i class="fas fa-folder text-warning"></i>
                        </span>
                        <span class="tree-label">${key}</span>
                    </div>
                    ${hasChildren ? `
                        <div class="tree-children" id="${nodeId}" style="display: none;">
                            ${renderTreeChildren(structure[key], level + 1, currentPath)}
                        </div>
                    ` : ''}
                </div>
            `;
        }
    });

    return html;
}

function countItems(structure) {
    let folders = 0;
    let files = 0;

    Object.keys(structure).forEach(key => {
        if (structure[key] === 'file') {
            files++;
        } else {
            folders++;
            const childCount = countItems(structure[key]);
            folders += childCount.folders;
            files += childCount.files;
        }
    });

    return { folders, files };
}

function getFileIcon(extension) {
    const iconMap = {
        // Video files
        'mp4': 'fas fa-file-video text-danger',
        'mov': 'fas fa-file-video text-danger',
        'avi': 'fas fa-file-video text-danger',
        'mkv': 'fas fa-file-video text-danger',
        'wmv': 'fas fa-file-video text-danger',
        'flv': 'fas fa-file-video text-danger',
        'webm': 'fas fa-file-video text-danger',

        // Audio files
        'mp3': 'fas fa-file-audio text-success',
        'wav': 'fas fa-file-audio text-success',
        'flac': 'fas fa-file-audio text-success',
        'aac': 'fas fa-file-audio text-success',

        // Image files
        'jpg': 'fas fa-file-image text-info',
        'jpeg': 'fas fa-file-image text-info',
        'png': 'fas fa-file-image text-info',
        'gif': 'fas fa-file-image text-info',
        'bmp': 'fas fa-file-image text-info',
        'svg': 'fas fa-file-image text-info',

        // Document files
        'pdf': 'fas fa-file-pdf text-danger',
        'doc': 'fas fa-file-word text-primary',
        'docx': 'fas fa-file-word text-primary',
        'txt': 'fas fa-file-alt text-secondary',
        'rtf': 'fas fa-file-alt text-secondary',

        // Archive files
        'zip': 'fas fa-file-archive text-warning',
        'rar': 'fas fa-file-archive text-warning',
        '7z': 'fas fa-file-archive text-warning',
        'tar': 'fas fa-file-archive text-warning',
        'gz': 'fas fa-file-archive text-warning',

        // Code files
        'js': 'fas fa-file-code text-warning',
        'html': 'fas fa-file-code text-warning',
        'css': 'fas fa-file-code text-info',
        'py': 'fas fa-file-code text-success',
        'json': 'fas fa-file-code text-warning',
        'xml': 'fas fa-file-code text-warning'
    };

    return iconMap[extension] || 'fas fa-file text-muted';
}

function getFileSize(filename) {
    // This is a placeholder - in a real implementation, you'd get actual file sizes
    const extensions = filename.split('.').pop().toLowerCase();
    const videoExts = ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'];
    const audioExts = ['mp3', 'wav', 'flac', 'aac'];
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'];

    if (videoExts.includes(extensions)) {
        return Math.floor(Math.random() * 500 + 100) + ' MB';
    } else if (audioExts.includes(extensions)) {
        return Math.floor(Math.random() * 50 + 5) + ' MB';
    } else if (imageExts.includes(extensions)) {
        return Math.floor(Math.random() * 10 + 1) + ' MB';
    } else {
        return Math.floor(Math.random() * 5 + 1) + ' KB';
    }
}

// Simple toggle function - 100% reliable
function toggleFolder(folderId) {
    console.log('Toggling folder:', folderId);

    const folderElement = document.getElementById(folderId);
    const toggle = document.querySelector(`[data-target="${folderId}"]`);

    if (!folderElement || !toggle) {
        console.error('Element not found:', folderId);
        return;
    }

    const icon = toggle.querySelector('i');
    const folderIcon = toggle.parentElement.querySelector('.tree-icon i');

    if (folderElement.style.display === 'none') {
        // Show folder
        folderElement.style.display = 'block';
        icon.className = 'fas fa-chevron-down';
        if (folderIcon) {
            folderIcon.className = 'fas fa-folder-open text-warning';
        }
        console.log('Expanded:', folderId);
    } else {
        // Hide folder
        folderElement.style.display = 'none';
        icon.className = 'fas fa-chevron-right';
        if (folderIcon) {
            folderIcon.className = 'fas fa-folder text-warning';
        }
        console.log('Collapsed:', folderId);
    }
}

// Make function globally available
window.toggleFolder = toggleFolder;

function initializeTreeInteractions() {
    const tree = document.getElementById('folderTree');

    // Add click handlers for selection only
    tree.addEventListener('click', function(e) {

        // Handle folder/file selection (skip if clicking toggle)
        if (e.target.closest('.tree-item') && !e.target.closest('.tree-toggle')) {
            const item = e.target.closest('.tree-item');
            const node = item.closest('.tree-node');

            // Remove previous selection
            tree.querySelectorAll('.tree-item').forEach(treeItem => {
                treeItem.classList.remove('selected');
            });

            // Add selection to clicked item
            item.classList.add('selected');

            // Update selected folder info
            const path = node.getAttribute('data-path');
            console.log('Selected:', path);

            // Show selection info
            showSelectionInfo(item, path);
        }
    });
}

function displayFolderStatistics(files, folderName) {
    const stats = calculateFolderStats(files);
    const statsContainer = document.getElementById('folderStats');

    if (statsContainer) {
        statsContainer.innerHTML = `
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-folder fa-2x mb-2"></i>
                            <h5 class="card-title">${stats.totalFolders}</h5>
                            <p class="card-text">Folders</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-file fa-2x mb-2"></i>
                            <h5 class="card-title">${stats.totalFiles}</h5>
                            <p class="card-text">Files</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-file-video fa-2x mb-2"></i>
                            <h5 class="card-title">${stats.videoFiles}</h5>
                            <p class="card-text">Video Files</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-hdd fa-2x mb-2"></i>
                            <h5 class="card-title">${stats.estimatedSize}</h5>
                            <p class="card-text">Est. Size</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

function calculateFolderStats(files) {
    const stats = {
        totalFiles: files.length,
        totalFolders: 0,
        videoFiles: 0,
        audioFiles: 0,
        imageFiles: 0,
        otherFiles: 0,
        estimatedSize: '0 MB'
    };

    const folders = new Set();
    let totalSizeBytes = 0;

    Array.from(files).forEach(file => {
        // Count unique folders
        const pathParts = file.webkitRelativePath.split('/');
        for (let i = 1; i < pathParts.length; i++) {
            const folderPath = pathParts.slice(0, i + 1).join('/');
            folders.add(folderPath);
        }

        // Categorize files by extension
        const extension = file.name.split('.').pop().toLowerCase();
        const videoExts = ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'];
        const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'];
        const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff'];

        if (videoExts.includes(extension)) {
            stats.videoFiles++;
            totalSizeBytes += file.size || (Math.random() * 500 + 100) * 1024 * 1024; // Estimate if size not available
        } else if (audioExts.includes(extension)) {
            stats.audioFiles++;
            totalSizeBytes += file.size || (Math.random() * 50 + 5) * 1024 * 1024;
        } else if (imageExts.includes(extension)) {
            stats.imageFiles++;
            totalSizeBytes += file.size || (Math.random() * 10 + 1) * 1024 * 1024;
        } else {
            stats.otherFiles++;
            totalSizeBytes += file.size || (Math.random() * 5 + 1) * 1024;
        }
    });

    stats.totalFolders = folders.size;

    // Format size
    if (totalSizeBytes > 1024 * 1024 * 1024) {
        stats.estimatedSize = (totalSizeBytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
    } else if (totalSizeBytes > 1024 * 1024) {
        stats.estimatedSize = (totalSizeBytes / (1024 * 1024)).toFixed(1) + ' MB';
    } else if (totalSizeBytes > 1024) {
        stats.estimatedSize = (totalSizeBytes / 1024).toFixed(1) + ' KB';
    } else {
        stats.estimatedSize = totalSizeBytes + ' B';
    }

    return stats;
}

function showSelectionInfo(item, path) {
    const selectionInfo = document.getElementById('selectionInfo');
    if (selectionInfo) {
        const isFolder = item.classList.contains('folder-item');
        const isFile = item.classList.contains('file-item');
        const label = item.querySelector('.tree-label').textContent;

        selectionInfo.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-${isFolder ? 'folder' : 'file'} me-2"></i>
                <strong>Selected:</strong> ${label}
                <br>
                <small class="text-muted">Path: ${path}</small>
                ${isFolder ? '<br><small class="text-muted">Type: Folder</small>' : ''}
                ${isFile ? '<br><small class="text-muted">Type: File</small>' : ''}
            </div>
        `;
    }
}

// Server folder structure functions
function displayServerFolderStructure(items, folderName, folderPath) {
    const tree = document.getElementById('folderTree');

    // Create a simple tree structure for server folders
    let html = `
        <div class="tree-item folder-item" data-path="${folderPath}">
            <div class="tree-content">
                <i class="fas fa-folder text-warning me-2"></i>
                <span class="tree-label">${folderName}</span>
                <span class="badge bg-primary ms-2">${items.length} items</span>
            </div>
            <div class="tree-children ms-3">
    `;

    // Add folders first
    const folders = items.filter(item => item.type === 'folder');
    const files = items.filter(item => item.type === 'file');

    folders.forEach(folder => {
        html += `
            <div class="tree-item folder-item" data-path="${folder.path}">
                <div class="tree-content">
                    <i class="fas fa-folder text-warning me-2"></i>
                    <span class="tree-label">${folder.name}</span>
                    <small class="text-muted ms-2">Folder</small>
                </div>
            </div>
        `;
    });

    // Add files
    files.forEach(file => {
        const icon = getFileIcon(file.name);
        html += `
            <div class="tree-item file-item" data-path="${file.path}">
                <div class="tree-content">
                    <i class="fas ${icon} me-2"></i>
                    <span class="tree-label">${file.name}</span>
                    <small class="text-muted ms-2">${formatFileSize(file.size)}</small>
                </div>
            </div>
        `;
    });

    html += `
            </div>
        </div>
    `;

    tree.innerHTML = html;

    // Initialize tree interactions
    initializeTreeInteractions();
}

function displayServerFolderStatistics(items, folderName) {
    const stats = calculateServerFolderStats(items);
    const statsContainer = document.getElementById('folderStats');

    if (statsContainer) {
        statsContainer.innerHTML = `
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-folder fa-2x mb-2"></i>
                            <h5 class="card-title">${stats.totalFolders}</h5>
                            <p class="card-text">Folders</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-file fa-2x mb-2"></i>
                            <h5 class="card-title">${stats.totalFiles}</h5>
                            <p class="card-text">Files</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-file-video fa-2x mb-2"></i>
                            <h5 class="card-title">${stats.videoFiles}</h5>
                            <p class="card-text">Video Files</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-hdd fa-2x mb-2"></i>
                            <h5 class="card-title">${stats.estimatedSize}</h5>
                            <p class="card-text">Est. Size</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

function calculateServerFolderStats(items) {
    const stats = {
        totalFiles: 0,
        totalFolders: 0,
        videoFiles: 0,
        audioFiles: 0,
        imageFiles: 0,
        otherFiles: 0,
        estimatedSize: '0 MB'
    };

    let totalSizeBytes = 0;

    items.forEach(item => {
        if (item.type === 'folder') {
            stats.totalFolders++;
        } else {
            stats.totalFiles++;
            totalSizeBytes += item.size || 0;

            const ext = item.name.toLowerCase().split('.').pop();
            if (['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'].includes(ext)) {
                stats.videoFiles++;
            } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].includes(ext)) {
                stats.audioFiles++;
            } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) {
                stats.imageFiles++;
            } else {
                stats.otherFiles++;
            }
        }
    });

    // Format size
    if (totalSizeBytes > 1024 * 1024 * 1024) {
        stats.estimatedSize = (totalSizeBytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
    } else if (totalSizeBytes > 1024 * 1024) {
        stats.estimatedSize = (totalSizeBytes / (1024 * 1024)).toFixed(1) + ' MB';
    } else if (totalSizeBytes > 1024) {
        stats.estimatedSize = (totalSizeBytes / 1024).toFixed(1) + ' KB';
    } else {
        stats.estimatedSize = totalSizeBytes + ' B';
    }

    return stats;
}

function getFileIcon(filename) {
    const ext = filename.toLowerCase().split('.').pop();

    if (['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'].includes(ext)) {
        return 'fa-file-video text-danger';
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].includes(ext)) {
        return 'fa-file-audio text-info';
    } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) {
        return 'fa-file-image text-success';
    } else if (['txt', 'doc', 'docx', 'pdf', 'rtf'].includes(ext)) {
        return 'fa-file-alt text-primary';
    } else {
        return 'fa-file text-muted';
    }
}

function openMetadataForm() {
    if (!selectedFolder) {
        alert('Please select a folder first.');
        return;
    }

    // Create metadata form
    const formHTML = createMetadataForm();
    document.getElementById('metadataForm').innerHTML = formHTML;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('metadataModal'));
    modal.show();

    // Initialize enhanced soft rename field after modal is shown
    modal._element.addEventListener('shown.bs.modal', () => {
        initializeSoftRenamedField();
    });

    // Initialize form handlers
    initializeMetadataForm();

    // Auto-fill Project Name with selected folder name
    autoFillProjectName();
}

function createMetadataForm() {
    return `
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="ocdNumber" class="form-label">OCD Number</label>
                    <input type="text" class="form-control" id="ocdNumber" required>
                </div>

                <div class="mb-3">
                    <label for="audioCode" class="form-label">Audio Code</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="audioCode" required>
                        <button class="btn btn-outline-primary" type="button" id="autoPopulateAudioCode" onclick="autoPopulateAudioCode()">
                            <i class="fas fa-magic me-1"></i>Auto Populate Audio Code
                        </button>
                    </div>
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle me-1"></i>Click "Auto Populate" to get the next available audio code from Google Sheets
                    </small>
                </div>

                <div class="mb-3">
                    <label for="transcriptionStatus" class="form-label">Transcription Status</label>
                    <select class="form-select" id="transcriptionStatus" required>
                        <option value="">Select transcription status...</option>
                        <option value="Applicable">Applicable</option>
                        <option value="Already in Social Media">Already in Social Media</option>
                        <option value="Not Applicable">Not Applicable</option>
                        <option value="MSR VT Video">MSR VT Video</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="videoType" class="form-label">Type of Video</label>
                    <select class="form-select" id="videoType" required>
                        <option value="">Select video type...</option>
                        <option value="Talk">Talk</option>
                        <option value="Documentary">Documentary</option>
                        <option value="Insta-Reels">Insta-Reels</option>
                        <option value="Class">Class</option>
                        <option value="Interview">Interview</option>
                        <option value="Promo">Promo</option>
                        <option value="Daily-Mystic-Quote">Daily-Mystic-Quote</option>
                        <option value="Episode">Episode</option>
                        <option value="Q-And-A">Q-And-A</option>
                        <option value="Message">Message</option>
                        <option value="Intro">Intro</option>
                        <option value="Song">Song</option>
                        <option value="Glimpses">Glimpses</option>
                        <option value="Animation">Animation</option>
                        <option value="Sharings">Sharings</option>
                        <option value="Video-Book">Video-Book</option>
                        <option value="Teaser">Teaser</option>
                        <option value="Poem">Poem</option>
                        <option value="Telefilm">Telefilm</option>
                        <option value="Non-Ashram Videos">Non-Ashram Videos</option>
                        <option value="Event">Event</option>
                        <option value="Miscellaneous">Miscellaneous</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Distribution Type</label>
                    <div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="distributionType" id="socialMedia" value="social">
                            <label class="form-check-label" for="socialMedia">Social Media</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="distributionType" id="internal" value="internal">
                            <label class="form-check-label" for="internal">Internal</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div id="socialMediaFields" class="d-none">
                    <div class="mb-3">
                        <label for="videoId" class="form-label">Video ID</label>
                        <input type="text" class="form-control" id="videoId">
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="searchGoogleSheets()">
                            <i class="fas fa-search me-1"></i>Search in Google Sheets
                        </button>
                    </div>
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title">
                    </div>
                    
                    <div class="mb-3">
                        <label for="publishDate" class="form-label">Date</label>
                        <input type="date" class="form-control" id="publishDate">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="url" class="form-label">URL</label>
                        <input type="url" class="form-control" id="url">
                    </div>
                    
                    <div class="mb-3">
                        <label for="duration" class="form-label">Duration</label>
                        <input type="text" class="form-control" id="duration" placeholder="e.g., 01Min-54Secs">
                    </div>
                    
                    <div class="mb-3">
                        <label for="platform" class="form-label">Published Platform</label>
                        <input type="text" class="form-control" id="platform" readonly>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="transcriptionFile" class="form-label">Transcription File Name</label>
                    <input type="text" class="form-control" id="transcriptionFile">
                </div>
                
                <div class="mb-3">
                    <label for="component" class="form-label">Component</label>
                    <select class="form-select" id="component">
                        <option value="">Select component...</option>
                        <option value="Sadhguru">Sadhguru</option>
                        <option value="Non-Sadhguru">Non-Sadhguru</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="contentTags" class="form-label">Content Tags</label>
                    <select class="form-select" id="contentTags" multiple>
                        <option value="Career-Success">Career-Success</option>
                        <option value="Yoga-Spirituality">Yoga-Spirituality</option>
                        <option value="Celebrity">Celebrity</option>
                        <option value="Conscious Planet">Conscious Planet</option>
                        <option value="Culture-Tradition">Culture-Tradition</option>
                        <option value="Health-Fitness">Health-Fitness</option>
                        <option value="Isha-Related">Isha-Related</option>
                        <option value="Lifestyle">Lifestyle</option>
                        <option value="Miscellaneous">Miscellaneous</option>
                        <option value="Personal-Wellbeing">Personal-Wellbeing</option>
                        <option value="Relationship-Sexuality">Relationship-Sexuality</option>
                        <option value="Sadhguru-Related">Sadhguru-Related</option>
                        <option value="Social-Issues">Social-Issues</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="projectName" class="form-label">
                        Project Name
                        <span class="badge bg-success ms-2">
                            <i class="fas fa-magic me-1"></i>Auto-filled (Exact)
                        </span>
                    </label>
                    <input type="text" class="form-control" id="projectName" placeholder="Project name will be auto-filled with exact folder name">
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle me-1"></i>Auto-filled with exact folder name (preserves underscores, hyphens, and original formatting). Edit if needed for date lookup.
                    </small>
                </div>
            </div>

            <div class="col-md-4">
                <div class="mb-3">
                    <label for="projectDate" class="form-label">Project Date (auto-filled)</label>
                    <input type="text" class="form-control" id="projectDate" placeholder="DD-MMM-YYYY" readonly>
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle me-1"></i>Auto-populated from Artifacts sheet, editable if not found
                    </small>
                </div>
            </div>

            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button class="btn btn-primary" type="button" id="autoPopulateDateBtn"
                                onclick="autoPopulateDate()" title="Auto-populate date from Google Sheets">
                            <i class="fas fa-search me-1"></i>Auto Populate Date
                        </button>
                    </div>
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle me-1"></i>Search Artifacts sheet for project date
                    </small>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="language" class="form-label">Language</label>
                    <select class="form-select" id="language">
                        <option value="">Select language...</option>
                        <option value="English">English</option>
                        <option value="Not Applicable">Not Applicable</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="totalDuration" class="form-label">
                        Total Duration
                        <span class="badge bg-info ms-2">
                            <i class="fas fa-video me-1"></i>Auto-detect
                        </span>
                    </label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="totalDuration" placeholder="e.g., 25Mins-30Secs" readonly>
                        <button class="btn btn-outline-primary" type="button" id="browseDurationBtn" onclick="openVideoFileBrowser()">
                            <i class="fas fa-folder-open me-1"></i>Browse Video (E:\)
                        </button>
                        <button class="btn btn-outline-secondary" type="button" onclick="testMediaInfo()" title="Test MediaInfo">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle me-1"></i>Browse E:\ drive to select video file and auto-detect duration
                    </small>
                </div>
            </div>
        </div>

        <!-- New Fields Row: With Logo & Influencer -->
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="withLogo" class="form-label">
                        <i class="fas fa-image me-2 text-primary"></i>With Logo
                    </label>
                    <select class="form-select" id="withLogo">
                        <option value="">Select option...</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle me-1"></i>Indicates if content includes logo/branding
                    </small>
                </div>
            </div>

            <div class="col-md-6">
                <div class="mb-3">
                    <label for="influencer" class="form-label">
                        <i class="fas fa-star me-2 text-warning"></i>Influencer
                    </label>
                    <select class="form-select" id="influencer">
                        <option value="">Select option...</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                    <small class="form-text text-muted">
                        <i class="fas fa-info-circle me-1"></i>Indicates if content features influencer collaboration
                    </small>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="backupType" class="form-label">Backup Type</label>
                    <select class="form-select" id="backupType">
                        <option value="">Select backup type...</option>
                        <option value="Stems">Stems</option>
                        <option value="Consolidated">Consolidated</option>
                        <option value="Unconsolidated">Unconsolidated</option>
                        <option value="Mov">Mov</option>
                        <option value="Mp4">Mp4</option>
                        <option value="Trimmed">Trimmed</option>
                        <option value="Premiere-Pro-Transcoded">Premiere-Pro-Transcoded</option>
                        <option value="Edited-DVD">Edited-DVD</option>
                        <option value="Premiere-Pro-Trimmed">Premiere-Pro-Trimmed</option>
                        <option value="Not Applicable">Not Applicable</option>
                        <option value="Pending">Pending</option>
                        <option value="Edited">Edited</option>
                        <option value="SDI-Digitized_Edited">SDI-Digitized_Edited</option>
                        <option value="Unpublished">Unpublished</option>
                        <option value="Avi">Avi</option>
                        <option value="Mxf">Mxf</option>
                        <option value="Wav">Wav</option>
                    </select>
                </div>
            </div>
            
            <div class="col-12">
                <div class="mb-4">
                    <label for="softRenamed" class="form-label fw-bold text-primary">
                        <i class="fas fa-file-signature me-2"></i>Soft Renamed Field
                        <span class="badge bg-info ms-2">Editable</span>
                    </label>
                    <div class="soft-rename-container">
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-edit text-muted"></i>
                            </span>
                            <textarea
                                class="form-control soft-rename-field"
                                id="softRenamed"
                                rows="2"
                                placeholder="Click Auto Generate to create filename, then edit as needed..."
                                style="resize: vertical; min-height: 60px; font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.4;"
                            ></textarea>
                            <button type="button" class="btn btn-primary" onclick="generateSoftRenamed()" title="Auto Generate Filename">
                                <i class="fas fa-magic me-1"></i>Generate
                            </button>
                        </div>
                        <div class="soft-rename-tools mt-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="soft-rename-info">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Auto-generated filename is fully editable. Use the tools below for easy editing.
                                    </small>
                                </div>
                                <div class="soft-rename-actions">
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="clearSoftRenamed()" title="Clear Field">
                                        <i class="fas fa-eraser"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="copySoftRenamed()" title="Copy to Clipboard">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatSoftRenamed()" title="Format & Clean">
                                        <i class="fas fa-broom"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="soft-rename-preview mt-2 d-none">
                            <div class="alert alert-light border-start border-primary border-4 py-2">
                                <small class="text-muted">
                                    <strong>Preview:</strong> <span id="softRenamedPreview" class="font-monospace"></span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-end mt-4">
            <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-success" onclick="processAndSendForVerification()">
                <i class="fas fa-check me-2"></i>Process and Send for Verification
            </button>
        </div>
    `;
}

function initializeMetadataForm() {
    // Add click handler for auto-populate button
    const autoPopulateBtn = document.getElementById('autoPopulateAudioCode');
    if (autoPopulateBtn) {
        autoPopulateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            autoPopulateAudioCode();
        });
    }

    // Handle distribution type change
    document.querySelectorAll('input[name="distributionType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const socialMediaFields = document.getElementById('socialMediaFields');
            if (this.value === 'social') {
                socialMediaFields.classList.remove('d-none');
            } else {
                socialMediaFields.classList.add('d-none');
                // Set transcription file name same as audio file name for internal
                const audioCode = document.getElementById('audioCode').value;
                if (audioCode) {
                    document.getElementById('transcriptionFile').value = audioCode;
                }
            }
        });
    });

    // Auto-update transcription file name for internal distribution
    const audioCodeField = document.getElementById('audioCode');
    if (audioCodeField) {
        audioCodeField.addEventListener('input', function() {
            const distributionType = document.querySelector('input[name="distributionType"]:checked');
            if (distributionType && distributionType.value === 'internal') {
                document.getElementById('transcriptionFile').value = this.value;
            }
        });
    }
}

function searchGoogleSheets() {
    const videoId = document.getElementById('videoId').value.trim();
    if (!videoId) {
        alert('Please enter a Video ID first.');
        return;
    }

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Searching...';
    button.disabled = true;

    fetch('/api/search_google_sheets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ video_id: videoId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.found) {
            // Populate fields with found data
            document.getElementById('title').value = data.data.title || '';
            document.getElementById('publishDate').value = convertDateForInput(data.data.date) || '';
            document.getElementById('description').value = data.data.description || '';
            document.getElementById('url').value = data.data.url || '';
            document.getElementById('duration').value = data.data.duration || '';
            document.getElementById('transcriptionFile').value = data.data.transcription_file || '';
            document.getElementById('platform').value = data.data.platform || '';

            // Show success message
            showAlert('success', `Data found in ${data.data.sheet} sheet and populated!`);
        } else if (data.success && !data.found) {
            showAlert('warning', 'Video ID not found in any Google Sheet.');
        } else {
            showAlert('error', data.error || 'Error searching Google Sheets.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Network error while searching Google Sheets.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function convertDateForInput(dateString) {
    // Convert "30-June-2025" to "2025-06-30" for date input
    if (!dateString) return '';

    try {
        const months = {
            'January': '01', 'February': '02', 'March': '03', 'April': '04',
            'May': '05', 'June': '06', 'July': '07', 'August': '08',
            'September': '09', 'October': '10', 'November': '11', 'December': '12'
        };

        const parts = dateString.split('-');
        if (parts.length === 3) {
            const day = parts[0].padStart(2, '0');
            const month = months[parts[1]] || parts[1];
            const year = parts[2];
            return `${year}-${month}-${day}`;
        }
    } catch (e) {
        console.error('Date conversion error:', e);
    }

    return dateString;
}

function generateSoftRenamed() {
    const videoType = document.getElementById('videoType').value;
    const title = document.getElementById('title').value;
    const publishDate = document.getElementById('publishDate').value;
    const projectDate = document.getElementById('projectDate').value;
    const language = document.getElementById('language').value;
    const totalDuration = document.getElementById('totalDuration').value;
    const backupType = document.getElementById('backupType').value;
    const distributionType = document.querySelector('input[name="distributionType"]:checked')?.value;

    if (!videoType || !backupType) {
        alert('Please fill in Video Type and Backup Type first.');
        return;
    }

    let softRenamed = '';

    // Add video type
    softRenamed += videoType;

    // Add title if available
    if (title) {
        const formattedTitle = title.replace(/\s+/g, '-').replace(/[^a-zA-Z0-9\-]/g, '');
        softRenamed += '_' + capitalizeWords(formattedTitle);
    }

    // Add date (use publish date for social media, project date for internal)
    const dateToUse = distributionType === 'social' ? publishDate : projectDate;
    if (dateToUse) {
        const formattedDate = formatDateForFilename(dateToUse);
        softRenamed += '_' + formattedDate;
    }

    // Add language if not "Not Applicable"
    if (language && language !== 'Not Applicable') {
        softRenamed += '_' + language;
    }

    // Add duration
    if (totalDuration) {
        softRenamed += '_' + totalDuration;
    }

    // Add backup type
    softRenamed += '_' + backupType;

    // Set the generated value
    const softRenamedField = document.getElementById('softRenamed');
    softRenamedField.value = softRenamed;

    // Add visual feedback that it's editable
    softRenamedField.classList.add('border-success', 'soft-renamed-editable');
    softRenamedField.focus();

    // Update placeholder to indicate it's editable
    softRenamedField.placeholder = 'Auto-generated - You can edit this field';

    // Show success message with enhanced feedback
    showAlert('success', 'Soft renamed field generated successfully! You can now edit it if needed.');

    // Show preview
    updateSoftRenamedPreview();

    // Add character count
    updateSoftRenamedInfo();
}

// Enhanced utility functions for Soft Rename field
function clearSoftRenamed() {
    const field = document.getElementById('softRenamed');
    field.value = '';
    field.classList.remove('border-success', 'soft-renamed-editable');
    field.placeholder = 'Click Auto Generate to create filename, then edit as needed...';

    // Hide preview
    const preview = document.querySelector('.soft-rename-preview');
    if (preview) {
        preview.classList.add('d-none');
    }

    showAlert('info', 'Soft renamed field cleared');
    field.focus();
}

function copySoftRenamed() {
    const field = document.getElementById('softRenamed');
    const value = field.value.trim();

    if (!value) {
        showAlert('warning', 'No content to copy. Please generate or enter a filename first.');
        return;
    }

    // Copy to clipboard
    navigator.clipboard.writeText(value).then(() => {
        showAlert('success', 'Filename copied to clipboard!');

        // Visual feedback
        field.style.backgroundColor = '#e8f5e8';
        setTimeout(() => {
            field.style.backgroundColor = '';
        }, 1000);
    }).catch(() => {
        // Fallback for older browsers
        field.select();
        document.execCommand('copy');
        showAlert('success', 'Filename copied to clipboard!');
    });
}

function formatSoftRenamed() {
    const field = document.getElementById('softRenamed');
    let value = field.value.trim();

    if (!value) {
        showAlert('warning', 'No content to format. Please generate or enter a filename first.');
        return;
    }

    // Clean and format the filename
    value = value
        .replace(/\s+/g, '_')           // Replace spaces with underscores
        .replace(/[^a-zA-Z0-9\-_]/g, '') // Remove special characters
        .replace(/_+/g, '_')            // Replace multiple underscores with single
        .replace(/^_|_$/g, '');         // Remove leading/trailing underscores

    field.value = value;
    updateSoftRenamedPreview();
    updateSoftRenamedInfo();

    showAlert('success', 'Filename formatted and cleaned!');

    // Visual feedback
    field.classList.add('border-success');
    setTimeout(() => {
        field.classList.remove('border-success');
    }, 2000);
}

function updateSoftRenamedPreview() {
    const field = document.getElementById('softRenamed');
    const preview = document.getElementById('softRenamedPreview');
    const previewContainer = document.querySelector('.soft-rename-preview');

    if (!field || !preview || !previewContainer) return;

    const value = field.value.trim();

    if (value) {
        preview.textContent = value;
        previewContainer.classList.remove('d-none');
    } else {
        previewContainer.classList.add('d-none');
    }
}

function updateSoftRenamedInfo() {
    const field = document.getElementById('softRenamed');
    const value = field.value.trim();

    // Update character count in the info area
    const infoElement = document.querySelector('.soft-rename-info small');
    if (infoElement && value) {
        const charCount = value.length;
        const wordCount = value.split(/[_\-\s]+/).filter(word => word.length > 0).length;

        infoElement.innerHTML = `
            <i class="fas fa-info-circle me-1"></i>
            Auto-generated filename is fully editable.
            <span class="badge bg-secondary ms-2">${charCount} chars</span>
            <span class="badge bg-secondary ms-1">${wordCount} parts</span>
        `;
    } else if (infoElement) {
        infoElement.innerHTML = `
            <i class="fas fa-info-circle me-1"></i>
            Auto-generated filename is fully editable. Use the tools below for easy editing.
        `;
    }
}

// Add event listeners for real-time updates
function initializeSoftRenamedField() {
    const field = document.getElementById('softRenamed');
    if (field) {
        field.addEventListener('input', () => {
            updateSoftRenamedPreview();
            updateSoftRenamedInfo();
        });

        field.addEventListener('focus', () => {
            field.parentElement.parentElement.style.transform = 'scale(1.01)';
        });

        field.addEventListener('blur', () => {
            field.parentElement.parentElement.style.transform = 'scale(1)';
        });
    }
}

function capitalizeWords(str) {
    return str.split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join('-');
}

function formatDateForFilename(dateString) {
    // Convert "2025-06-30" to "30-Jun-2025"
    try {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = date.toLocaleDateString('en-US', { month: 'short' });
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    } catch (e) {
        return dateString;
    }
}

function processAndSendForVerification() {
    // Validate required fields
    const requiredFields = ['ocdNumber', 'audioCode', 'videoType'];
    const missingFields = [];

    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            missingFields.push(field.previousElementSibling.textContent);
        }
    });

    if (missingFields.length > 0) {
        alert('Please fill in the following required fields:\n' + missingFields.join('\n'));
        return;
    }

    // Validate soft renamed field
    const softRenamedValue = document.getElementById('softRenamed').value.trim();
    if (!softRenamedValue) {
        showAlert('error', 'Please generate or enter a Soft Renamed field before submitting.');
        return;
    }

    // Collect all form data
    const metadata = {
        ocd_number: document.getElementById('ocdNumber').value,
        audio_code: document.getElementById('audioCode').value,
        transcription_status: document.getElementById('transcriptionStatus').value,
        video_type: document.getElementById('videoType').value,
        distribution_type: document.querySelector('input[name="distributionType"]:checked')?.value,
        video_id: document.getElementById('videoId')?.value || '',
        title: document.getElementById('title')?.value || '',
        publish_date: document.getElementById('publishDate')?.value || '',
        description: document.getElementById('description')?.value || '',
        url: document.getElementById('url')?.value || '',
        duration: document.getElementById('duration')?.value || '',
        platform: document.getElementById('platform')?.value || '',
        transcription_file: document.getElementById('transcriptionFile').value,
        component: document.getElementById('component').value,
        content_tags: Array.from(document.getElementById('contentTags').selectedOptions).map(option => option.value),
        project_name: document.getElementById('projectName').value,
        project_date: document.getElementById('projectDate').value,
        language: document.getElementById('language').value,
        total_duration: document.getElementById('totalDuration').value,
        backup_type: document.getElementById('backupType').value,
        with_logo: document.getElementById('withLogo').value,
        influencer: document.getElementById('influencer').value,
        soft_renamed: document.getElementById('softRenamed').value
    };

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    button.disabled = true;

    // Send to server
    fetch('/api/process_folder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            folder_path: selectedFolder.path,
            folder_name: selectedFolder.name,
            metadata: metadata
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Folder processed and sent for verification successfully!');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('metadataModal'));
            modal.hide();

            // Refresh queue
            loadProcessingQueue();
            refreshStatistics();

            // Reset selected folder
            selectedFolder = null;
            document.getElementById('folderTreeContainer').classList.add('d-none');
            document.getElementById('processFolder').disabled = true;
        } else {
            showAlert('error', data.error || 'Error processing folder.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Network error while processing folder.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function loadProcessingQueue() {
    // This would load the processing queue data
    // For now, we'll just show a placeholder
    const tbody = document.getElementById('queueTableBody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    No items in processing queue
                </td>
            </tr>
        `;
    }
}

function refreshStatistics() {
    // This would refresh the dashboard statistics
    // Implementation would fetch updated counts from the server
    console.log('Refreshing statistics...');
}

function showAlert(type, message) {
    // Create and show alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the main container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Auto-populate Project Date from Google Sheets Artifacts sheet
function autoPopulateDate() {
    console.log('🔍 Auto-populating Project Date from Artifacts sheet...');

    // Get the project name from the input field
    const projectNameElement = document.getElementById('projectName');
    if (!projectNameElement || !projectNameElement.value.trim()) {
        showAlert('error', 'Please enter a Project Name first before auto-populating the date');
        return;
    }

    const projectName = projectNameElement.value.trim();
    console.log(`📁 Looking up project: ${projectName}`);

    // Show loading state
    const populateBtn = document.getElementById('autoPopulateDateBtn');
    const originalText = populateBtn.innerHTML;
    populateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Searching...';
    populateBtn.disabled = true;

    // Call the backend API to lookup the project date
    fetch('/api/lookup_project_date', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            project_name: projectName
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('📊 Lookup response:', data);

        if (data.success && data.project_date) {
            // Keep the original DD-MMM-YYYY format for display
            document.getElementById('projectDate').value = data.project_date;
            document.getElementById('projectDate').readOnly = false; // Make editable
            showAlert('success', `Project Date found and populated: ${data.project_date}`);
            console.log(`✅ Project Date set to: ${data.project_date}`);
        } else if (data.success && !data.project_date) {
            // No match found - make field editable for manual entry
            document.getElementById('projectDate').value = '';
            document.getElementById('projectDate').readOnly = false;
            document.getElementById('projectDate').placeholder = 'Enter date manually (DD-MMM-YYYY)';
            showAlert('warning', 'Project Name not found in Artifacts sheet.');
            console.log(`⚠️ No date found for project: ${projectName}`);
        } else {
            showAlert('error', data.error || 'Failed to lookup project date');
        }
    })
    .catch(error => {
        console.error('❌ Error looking up project date:', error);
        showAlert('error', 'Network error while looking up project date');
    })
    .finally(() => {
        // Restore button state
        populateBtn.innerHTML = originalText;
        populateBtn.disabled = false;
    });
}

// Convert DD-MMM-YYYY to YYYY-MM-DD format
function convertDateFormat(dateStr) {
    try {
        // Parse DD-MMM-YYYY format (e.g., "13-Feb-2011")
        const parts = dateStr.split('-');
        if (parts.length !== 3) {
            console.error('Invalid date format:', dateStr);
            return null;
        }

        const day = parts[0].padStart(2, '0');
        const monthStr = parts[1];
        const year = parts[2];

        // Convert month name to number
        const monthMap = {
            'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
            'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
            'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        };

        const month = monthMap[monthStr];
        if (!month) {
            console.error('Invalid month:', monthStr);
            return null;
        }

        // Return in YYYY-MM-DD format
        const formattedDate = `${year}-${month}-${day}`;
        console.log(`📅 Converted ${dateStr} to ${formattedDate}`);
        return formattedDate;

    } catch (error) {
        console.error('Error converting date format:', error);
        return null;
    }
}

// Audio Code Auto-Populate Function
async function autoPopulateAudioCode() {
    console.log('🎯 Auto-populating audio code from Google Sheets...');

    const button = document.getElementById('autoPopulateAudioCode');
    const audioCodeField = document.getElementById('audioCode');

    if (!button) {
        console.error('❌ Auto-populate button not found!');
        showAlert('error', 'Auto-populate button not found!');
        return;
    }

    if (!audioCodeField) {
        console.error('❌ Audio code field not found!');
        showAlert('error', 'Audio code field not found!');
        return;
    }

    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
    button.disabled = true;

    try {
        const response = await fetch('/api/auto_populate_audio_code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            // Populate the audio code field
            audioCodeField.value = result.audio_code;
            audioCodeField.classList.add('is-valid');

            // Show success message
            showAlert('success', `Audio code ${result.audio_code} has been assigned and marked as Blocked`);

            console.log('✅ Audio code auto-populated:', result.audio_code);

            // Trigger any dependent field updates
            audioCodeField.dispatchEvent(new Event('input'));

        } else {
            // Show error message
            showAlert('error', result.error || 'Failed to auto-populate audio code');
            console.error('❌ Auto-populate failed:', result.error);
        }

    } catch (error) {
        console.error('❌ Network error:', error);
        showAlert('error', 'Network error occurred while auto-populating audio code');
    } finally {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Make function globally available
window.autoPopulateAudioCode = autoPopulateAudioCode;

// Browse video file for duration detection
function browseVideoForDuration() {
    console.log('🎬 Opening video file browser for duration detection...');

    try {
        // Create file input for video files
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.mov,.mp4,.avi,.mkv,.wmv,.flv,.webm,.m4v,.3gp,.ogv';
        input.style.display = 'none';

        input.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const fileSizeMB = file.size / 1024 / 1024;
                console.log(`📹 Selected video file: ${file.name} (${fileSizeMB.toFixed(2)} MB)`);
                console.log(`📹 File type: ${file.type}`);

                // Warn about large files
                if (fileSizeMB > 500) {
                    const proceed = confirm(`Large file detected (${fileSizeMB.toFixed(2)} MB). This may take several minutes to process. Continue?`);
                    if (!proceed) {
                        console.log('❌ User cancelled large file processing');
                        return;
                    }
                }

                detectVideoDuration(file);
            } else {
                console.log('❌ No file selected');
            }
        });

        document.body.appendChild(input);
        input.click();
        document.body.removeChild(input);
    } catch (error) {
        console.error('❌ Error in browseVideoForDuration:', error);
        alert('Error opening file browser: ' + error.message);
    }
}

// Make function globally available
window.browseVideoForDuration = browseVideoForDuration;

// Test MediaInfo availability
function testMediaInfo() {
    console.log('🔧 Testing MediaInfo availability...');

    fetch('/api/test_mediainfo')
    .then(response => response.json())
    .then(data => {
        console.log('🔧 MediaInfo test result:', data);

        if (data.success && data.mediainfo_available) {
            showAlert('success', `MediaInfo is available: ${data.version}`);
            console.log('✅ MediaInfo test passed:', data);
        } else {
            showAlert('error', `MediaInfo not available: ${data.error}`);
            console.error('❌ MediaInfo test failed:', data);
        }
    })
    .catch(error => {
        console.error('❌ MediaInfo test error:', error);
        showAlert('error', 'Error testing MediaInfo: ' + error.message);
    });
}

// Make function globally available
window.testMediaInfo = testMediaInfo;

// Test simple endpoint
function testSimpleEndpoint() {
    console.log('🔧 Testing simple endpoint...');

    fetch('/api/test_simple', {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('🔧 Simple test response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('🔧 Simple test result:', data);
        showAlert('success', `Simple endpoint working: ${data.message}`);
    })
    .catch(error => {
        console.error('❌ Simple test error:', error);
        showAlert('error', 'Simple endpoint failed: ' + error.message);
    });
}

// Test video duration with simple endpoint
function testVideoDurationSimple() {
    console.log('🔧 Testing simple video duration endpoint...');

    // Create file input
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.mov,.mp4,.avi,.mkv,.wmv,.flv,.webm,.m4v,.3gp,.ogv';

    input.addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            console.log(`🔧 Testing with file: ${file.name}`);

            const formData = new FormData();
            formData.append('video_file', file);

            fetch('/api/test_video_duration', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('🔧 Test video response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('🔧 Test video result:', data);
                if (data.success) {
                    showAlert('success', `Test successful: ${data.message}`);
                } else {
                    showAlert('error', `Test failed: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('❌ Test video error:', error);
                showAlert('error', 'Test video failed: ' + error.message);
            });
        }
    });

    input.click();
}

// Make functions globally available
window.testSimpleEndpoint = testSimpleEndpoint;
window.testVideoDurationSimple = testVideoDurationSimple;

// Video file browser for duration detection
function openVideoFileBrowser() {
    console.log('🎬 Opening video file browser for duration detection...');
    showVideoFileBrowserModal();
}

function showVideoFileBrowserModal() {
    const modalId = 'videoFileBrowserModal';

    // Remove existing modal if any
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const modalHTML = `
        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="${modalId}Label">
                            <i class="fas fa-video me-2"></i>Select Video File for Duration Detection
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="currentVideoPath" class="form-label">Current Path:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="currentVideoPath" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="loadVideoDirectory('E:\\\\')">
                                    <i class="fas fa-home me-1"></i>E:\ Root
                                </button>
                            </div>
                        </div>
                        <div id="videoFileBrowserError" class="alert alert-danger d-none"></div>
                        <div id="videoFileBrowserContent" style="max-height: 400px; overflow-y: auto;">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add custom CSS for ultra beautiful soft rename field
    const softRenameCSS = `
        <style id="softRenameStyles">
            .soft-rename-container {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-radius: 12px;
                padding: 20px;
                border: 2px solid #e9ecef;
                transition: all 0.3s ease;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }

            .soft-rename-container:hover {
                border-color: #007bff;
                box-shadow: 0 4px 20px rgba(0,123,255,0.1);
            }

            .soft-rename-field {
                border: 2px solid #dee2e6 !important;
                border-radius: 8px !important;
                transition: all 0.3s ease !important;
                background: #ffffff !important;
                box-shadow: inset 0 2px 4px rgba(0,0,0,0.05) !important;
            }

            .soft-rename-field:focus {
                border-color: #007bff !important;
                box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25), inset 0 2px 4px rgba(0,0,0,0.05) !important;
                background: #ffffff !important;
            }

            .soft-rename-field.border-success {
                border-color: #28a745 !important;
                background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%) !important;
                animation: softRenameGenerated 0.6s ease;
            }

            @keyframes softRenameGenerated {
                0% { transform: scale(1); }
                50% { transform: scale(1.02); }
                100% { transform: scale(1); }
            }

            .soft-rename-tools {
                background: rgba(255,255,255,0.7);
                border-radius: 8px;
                padding: 12px;
                border: 1px solid rgba(0,0,0,0.1);
            }

            .soft-rename-actions .btn {
                transition: all 0.2s ease;
                border-radius: 6px;
            }

            .soft-rename-actions .btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            }

            .soft-rename-preview {
                animation: fadeInUp 0.3s ease;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .soft-rename-info small {
                font-weight: 500;
            }

            .input-group-text {
                border: 2px solid #dee2e6;
                border-right: none;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            }

            .soft-rename-container .btn-primary {
                border-radius: 0 8px 8px 0;
                border: 2px solid #007bff;
                border-left: none;
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                transition: all 0.3s ease;
            }

            .soft-rename-container .btn-primary:hover {
                background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            }
        </style>
    `;

    // Add CSS to head if not already added
    if (!document.getElementById('softRenameStyles')) {
        document.head.insertAdjacentHTML('beforeend', softRenameCSS);
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    // Load initial directory (start at E:\ drive only)
    loadVideoDirectory('E:\\');
}

async function loadVideoDirectory(path) {
    try {
        // Ensure we always start from E:\Work space (restricted for executors)
        if (!path || path === '/' || path === '' || path === 'drives') {
            path = 'E:\\Work space';
        }

        const response = await fetch('/api/browse_files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                path: path,
                type: 'video'  // Only show video files and folders
            })
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('currentVideoPath').value = data.path;
            displayVideoItems(data.items);
        } else {
            showVideoError(`Error: ${data.error}`);
        }
    } catch (error) {
        showVideoError(`Network error: ${error.message}`);
    }
}

function displayVideoItems(items) {
    const container = document.getElementById('videoFileBrowserContent');

    if (!items || items.length === 0) {
        container.innerHTML = '<div class="text-muted text-center p-3">No video files or folders found</div>';
        return;
    }

    let html = '<div class="list-group">';

    items.forEach(item => {
        const isFolder = item.type === 'folder';
        const isVideo = item.type === 'file' && item.name.match(/\.(mov|mp4|avi|mkv|wmv|flv|webm|m4v|3gp|ogv)$/i);

        if (!isFolder && !isVideo) return; // Skip non-video files

        const icon = isFolder ? 'fas fa-folder text-warning' : 'fas fa-video text-primary';
        const clickAction = isFolder ? `loadVideoDirectory('${item.path.replace(/\\/g, '\\\\')}')` : `selectVideoFile('${item.path.replace(/\\/g, '\\\\')}')`;
        const textClass = isFolder ? 'fw-bold' : '';

        html += `
            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center cursor-pointer"
                 onclick="${clickAction}">
                <div class="${textClass}">
                    <i class="${icon} me-2"></i>
                    ${item.name}
                </div>
                <small class="text-muted">
                    ${isFolder ? 'Folder' : formatFileSize(item.size)}
                </small>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

function selectVideoFile(filePath) {
    console.log(`🎬 Selected video file: ${filePath}`);

    // Close the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('videoFileBrowserModal'));
    modal.hide();

    // Detect duration using the server-side path
    detectVideoDurationFromPath(filePath);
}

function showVideoError(message) {
    const errorDiv = document.getElementById('videoFileBrowserError');
    errorDiv.textContent = message;
    errorDiv.classList.remove('d-none');

    const contentDiv = document.getElementById('videoFileBrowserContent');
    contentDiv.innerHTML = '<div class="text-center text-muted p-3">Error loading directory</div>';
}

function formatFileSize(bytes) {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Detect video duration from server file path
function detectVideoDurationFromPath(filePath) {
    console.log(`🔍 Detecting duration from server path: ${filePath}`);

    const durationField = document.getElementById('totalDuration');
    const browseBtn = document.getElementById('browseDurationBtn');
    const originalBtnText = browseBtn.innerHTML;

    // Show loading state
    durationField.value = 'Detecting duration...';
    browseBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
    browseBtn.disabled = true;

    fetch('/api/detect_duration_from_path', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            file_path: filePath
        }),
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('📊 Duration detection response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('📊 Duration detection response:', data);

        if (data.success && data.duration) {
            console.log('✅ Duration detected successfully:', data.duration);

            durationField.value = data.duration;

            // Add visual feedback
            durationField.style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                durationField.style.backgroundColor = '';
            }, 2000);

            showAlert('success', `Duration detected: ${data.duration} from ${data.filename}`);
            console.log(`✅ Duration detection complete: ${data.raw_duration} → ${data.duration}`);
        } else {
            console.error('❌ Duration detection failed:', data.error || 'Unknown error');
            durationField.value = '';
            showAlert('error', data.error || 'Failed to detect video duration');
        }
    })
    .catch(error => {
        console.error('❌ Duration detection error:', error);
        durationField.value = '';
        showAlert('error', 'Error detecting video duration: ' + error.message);
    })
    .finally(() => {
        console.log('🔄 Restoring button state...');
        // Restore button state
        browseBtn.innerHTML = originalBtnText;
        browseBtn.disabled = false;
    });
}

// Test authentication
function testAuth() {
    console.log('🔐 Testing authentication...');

    fetch('/api/test_auth', {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('🔐 Auth test response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('🔐 Auth test result:', data);
        if (data.success) {
            showAlert('success', `Auth OK: ${data.username} (${data.role})`);
        } else {
            showAlert('error', 'Auth failed: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('❌ Auth test error:', error);
        showAlert('error', 'Auth test failed: ' + error.message);
    });
}

// Make video browser functions globally available
window.openVideoFileBrowser = openVideoFileBrowser;
window.loadVideoDirectory = loadVideoDirectory;
window.selectVideoFile = selectVideoFile;
window.detectVideoDurationFromPath = detectVideoDurationFromPath;
window.testAuth = testAuth;

// Make soft rename utility functions globally available
window.clearSoftRenamed = clearSoftRenamed;
window.copySoftRenamed = copySoftRenamed;
window.formatSoftRenamed = formatSoftRenamed;
window.updateSoftRenamedPreview = updateSoftRenamedPreview;
window.updateSoftRenamedInfo = updateSoftRenamedInfo;
window.initializeSoftRenamedField = initializeSoftRenamedField;

// Detect video duration using MediaInfo
function detectVideoDuration(file) {
    console.log('🔍 Detecting video duration with MediaInfo...');
    console.log('📹 File details:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
    });

    try {
        const formData = new FormData();
        formData.append('video_file', file);

        // Show loading state
        const durationField = document.getElementById('totalDuration');
        const browseBtn = document.getElementById('browseDurationBtn');

        if (!durationField) {
            console.error('❌ Duration field not found!');
            alert('Error: Duration field not found');
            return;
        }

        if (!browseBtn) {
            console.error('❌ Browse button not found!');
            alert('Error: Browse button not found');
            return;
        }

        const originalBtnText = browseBtn.innerHTML;

        console.log('🔄 Setting loading state...');
        durationField.value = 'Detecting duration...';
        browseBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
        browseBtn.disabled = true;

        console.log('📡 Sending request to /api/detect_video_duration...');

        // Create AbortController for timeout handling
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minute timeout

        fetch('http://localhost:5008/video', {
            method: 'POST',
            body: formData,
            signal: controller.signal
        })
        .then(response => {
            console.log('📡 Response status:', response.status);
            console.log('📡 Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.json();
        })
        .then(data => {
            console.log('📊 Duration detection response:', data);

            if (data.success && data.duration) {
                console.log('✅ Duration detected successfully:', data.duration);
                const formattedDuration = formatDuration(data.duration);
                console.log('✅ Formatted duration:', formattedDuration);

                durationField.value = formattedDuration;

                // Add visual feedback
                durationField.style.backgroundColor = '#e8f5e8';
                setTimeout(() => {
                    durationField.style.backgroundColor = '';
                }, 2000);

                showAlert('success', `Duration detected: ${formattedDuration} from ${file.name}`);
                console.log(`✅ Duration detection complete: ${data.duration} → ${formattedDuration}`);
            } else {
                console.error('❌ Duration detection failed:', data.error || 'Unknown error');
                durationField.value = '';
                showAlert('error', data.error || 'Failed to detect video duration');
            }
        })
        .catch(error => {
            console.error('❌ Duration detection error:', error);
            durationField.value = '';

            if (error.name === 'AbortError') {
                showAlert('error', 'Video duration detection timed out. Please try with a smaller file.');
            } else if (error.message.includes('ERR_ACCESS_DENIED')) {
                showAlert('error', 'Access denied. File may be too large or server configuration issue.');
            } else {
                showAlert('error', 'Error detecting video duration: ' + error.message);
            }
        })
        .finally(() => {
            console.log('🔄 Restoring button state...');
            clearTimeout(timeoutId);
            // Restore button state
            browseBtn.innerHTML = originalBtnText;
            browseBtn.disabled = false;
        });

    } catch (error) {
        console.error('❌ Error in detectVideoDuration:', error);
        alert('Error in duration detection: ' + error.message);
    }
}

// Format duration from HH:MM:SS to XXMins-XXSecs
function formatDuration(duration) {
    console.log(`🕐 Formatting duration: ${duration}`);

    // Parse duration (expected format: HH:MM:SS or MM:SS or SS)
    const parts = duration.split(':').map(part => parseInt(part, 10));

    let totalSeconds = 0;
    let minutes = 0;
    let seconds = 0;

    if (parts.length === 3) {
        // HH:MM:SS format
        const hours = parts[0];
        minutes = parts[1];
        seconds = parts[2];

        // Convert hours to minutes
        totalSeconds = seconds;
        minutes = minutes + (hours * 60);
    } else if (parts.length === 2) {
        // MM:SS format
        minutes = parts[0];
        seconds = parts[1];
        totalSeconds = seconds;
    } else if (parts.length === 1) {
        // SS format (less than a minute)
        totalSeconds = parts[0];
        minutes = 0;
        seconds = totalSeconds;
    }

    // Format according to rules
    let formatted = '';

    if (minutes === 0 && totalSeconds > 0) {
        // Less than a minute: "53Secs"
        formatted = `${totalSeconds.toString().padStart(2, '0')}${totalSeconds === 1 ? 'Sec' : 'Secs'}`;
    } else if (minutes > 0) {
        // Has minutes
        const minText = minutes === 1 ? 'Min' : 'Mins';
        const secText = totalSeconds === 1 ? 'Sec' : 'Secs';

        if (totalSeconds === 0) {
            // Exact minutes: "05Mins"
            formatted = `${minutes.toString().padStart(2, '0')}${minText}`;
        } else {
            // Minutes and seconds: "02Mins-01Sec" or "72Mins-54Secs"
            formatted = `${minutes.toString().padStart(2, '0')}${minText}-${totalSeconds.toString().padStart(2, '0')}${secText}`;
        }
    } else {
        // Zero duration
        formatted = '00Secs';
    }

    console.log(`✅ Formatted duration: ${duration} → ${formatted}`);
    return formatted;
}

// Auto-fill Project Name with selected folder name (exact match)
function autoFillProjectName() {
    if (!selectedFolder || !selectedFolder.name) {
        console.log('⚠️ No selected folder or folder name available');
        return;
    }

    const projectNameField = document.getElementById('projectName');
    if (!projectNameField) {
        console.error('❌ Project Name field not found');
        return;
    }

    // Use the exact folder name without any modifications
    const exactFolderName = selectedFolder.name;

    // Set the project name field with exact folder name
    projectNameField.value = exactFolderName;

    // Add visual feedback
    projectNameField.style.backgroundColor = '#e8f5e8';
    setTimeout(() => {
        projectNameField.style.backgroundColor = '';
    }, 2000);

    console.log(`✅ Auto-filled Project Name: "${exactFolderName}" (exact folder name preserved)`);

    // Show success message
    showAlert('success', `Project Name auto-filled with exact folder name: "${exactFolderName}"`);
}

// Simple Expand/Collapse All Functions
function expandAllNodes() {
    console.log('Expanding all folders...');

    const allFolders = document.querySelectorAll('.tree-children');
    const allToggles = document.querySelectorAll('.tree-toggle');

    allFolders.forEach(folder => {
        if (folder.id !== 'root-children') { // Don't affect root
            folder.style.display = 'block';
        }
    });

    allToggles.forEach(toggle => {
        const icon = toggle.querySelector('i');
        const folderIcon = toggle.parentElement.querySelector('.tree-icon i');

        if (icon) {
            icon.className = 'fas fa-chevron-down';
        }
        if (folderIcon) {
            folderIcon.className = 'fas fa-folder-open text-warning';
        }
    });

    console.log('✅ All folders expanded');
}

function collapseAllNodes() {
    console.log('Collapsing all folders...');

    const allFolders = document.querySelectorAll('.tree-children');
    const allToggles = document.querySelectorAll('.tree-toggle');

    allFolders.forEach(folder => {
        if (folder.id !== 'root-children') { // Don't affect root
            folder.style.display = 'none';
        }
    });

    allToggles.forEach(toggle => {
        const icon = toggle.querySelector('i');
        const folderIcon = toggle.parentElement.querySelector('.tree-icon i');

        if (icon) {
            icon.className = 'fas fa-chevron-right';
        }
        if (folderIcon) {
            folderIcon.className = 'fas fa-folder text-warning';
        }
    });

    console.log('✅ All folders collapsed');
}

// Simple debug function
function debugTree() {
    const toggles = document.querySelectorAll('.tree-toggle');
    const folders = document.querySelectorAll('.tree-children');

    console.log('🌳 Tree Debug:');
    console.log('Toggles:', toggles.length);
    console.log('Folders:', folders.length);

    toggles.forEach((toggle, i) => {
        const targetId = toggle.getAttribute('data-target');
        const target = document.getElementById(targetId);
        console.log(`Toggle ${i + 1}: ${targetId} -> ${target ? 'Found' : 'Missing'}`);
    });
}

window.debugTree = debugTree;

// Make server-side folder browser functions globally available
window.loadExecutorBrowserDirectory = loadExecutorBrowserDirectory;
window.navigateExecutorUp = navigateExecutorUp;
window.refreshExecutorBrowser = refreshExecutorBrowser;
window.selectExecutorCurrentFolder = selectExecutorCurrentFolder;
