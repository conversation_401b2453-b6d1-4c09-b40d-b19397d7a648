// Admin Dashboard JavaScript

let currentUserId = null;
let allRecords = [];

document.addEventListener('DOMContentLoaded', function() {
    initializeAdminDashboard();
});

function initializeAdminDashboard() {
    console.log('Admin Dashboard initialized');
    
    // Initialize charts
    initializeCharts();
    
    // Set up user form submission
    document.getElementById('saveUserBtn').addEventListener('click', saveUser);
    
    // Auto-refresh activity feed every 30 seconds
    setInterval(refreshActivity, 30000);
}

// Global chart instances for updates
let dailyProcessingChart = null;
let crosscheckerChart = null;
let editorChart = null;
let workflowChart = null;

function initializeCharts() {
    console.log('📊 Initializing admin performance charts...');

    // Initialize charts with loading state
    initializeDailyProcessingChart();
    initializeCrosscheckerChart();
    initializeEditorChart();
    initializeWorkflowChart();

    // Load real data
    loadPerformanceData();

    // Set up auto-refresh every 60 seconds
    setInterval(loadPerformanceData, 60000);
}

function initializeDailyProcessingChart() {
    const dailyCtx = document.getElementById('dailyProcessingChart');
    if (dailyCtx) {
        dailyProcessingChart = new Chart(dailyCtx, {
            type: 'bar',
            data: {
                labels: ['Loading...'],
                datasets: [{
                    label: 'Executor 1',
                    data: [0],
                    backgroundColor: 'rgba(78, 115, 223, 0.8)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1
                }, {
                    label: 'Executor 2',
                    data: [0],
                    backgroundColor: 'rgba(28, 200, 138, 0.8)',
                    borderColor: 'rgba(28, 200, 138, 1)',
                    borderWidth: 1
                }, {
                    label: 'Executor 3',
                    data: [0],
                    backgroundColor: 'rgba(255, 193, 7, 0.8)',
                    borderColor: 'rgba(255, 193, 7, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Processing by Executors (Last 7 Days)'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Files Processed'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Day of Week'
                        }
                    }
                }
            }
        });
    }
}

function initializeCrosscheckerChart() {
    const crossCtx = document.getElementById('crosscheckerChart');
    if (crossCtx) {
        crosscheckerChart = new Chart(crossCtx, {
            type: 'line',
            data: {
                labels: ['Loading...'],
                datasets: [{
                    label: 'Verified',
                    data: [0],
                    borderColor: 'rgba(28, 200, 138, 1)',
                    backgroundColor: 'rgba(28, 200, 138, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'Rejected',
                    data: [0],
                    borderColor: 'rgba(231, 74, 59, 1)',
                    backgroundColor: 'rgba(231, 74, 59, 0.1)',
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Cross-Checker Activity Trends (Last 7 Days)'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Items'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Day of Week'
                        }
                    }
                }
            }
        });
    }
}

function initializeEditorChart() {
    const editorCtx = document.getElementById('editorChart');
    if (editorCtx) {
        editorChart = new Chart(editorCtx, {
            type: 'line',
            data: {
                labels: ['Loading...'],
                datasets: [{
                    label: 'Completed',
                    data: [0],
                    borderColor: 'rgba(246, 194, 62, 1)',
                    backgroundColor: 'rgba(246, 194, 62, 0.1)',
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Editor Progress (Last 7 Days)'
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Items Completed'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Day of Week'
                        }
                    }
                }
            }
        });
    }
}

function initializeWorkflowChart() {
    const workflowCtx = document.getElementById('workflowStatusChart');
    if (workflowCtx) {
        workflowChart = new Chart(workflowCtx, {
            type: 'doughnut',
            data: {
                labels: ['Pending Crosscheck', 'Verified', 'Rejected', 'Pending Editing', 'Editing Completed'],
                datasets: [{
                    data: [0, 0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(78, 115, 223, 0.8)',
                        'rgba(28, 200, 138, 0.8)',
                        'rgba(231, 74, 59, 0.8)',
                        'rgba(246, 194, 62, 0.8)',
                        'rgba(54, 185, 204, 0.8)'
                    ],
                    borderColor: [
                        'rgba(78, 115, 223, 1)',
                        'rgba(28, 200, 138, 1)',
                        'rgba(231, 74, 59, 1)',
                        'rgba(246, 194, 62, 1)',
                        'rgba(54, 185, 204, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Current Workflow Status'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// Load performance data from API
function loadPerformanceData() {
    console.log('📊 Loading performance data...');

    fetch('/api/admin/performance')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDailyProcessingChart(data.daily_processing);
                updateCrosscheckerChart(data.crosschecker_activity);
                updateEditorChart(data.editor_progress);
                updateWorkflowChart(data.workflow_status);

                console.log('📊 Performance data updated successfully');
            } else {
                console.error('❌ Failed to load performance data:', data.error);
            }
        })
        .catch(error => {
            console.error('❌ Error loading performance data:', error);
        });
}

// Update chart functions
function updateDailyProcessingChart(data) {
    if (dailyProcessingChart && data) {
        dailyProcessingChart.data.labels = data.labels;
        dailyProcessingChart.data.datasets[0].data = data.datasets.executor1;
        dailyProcessingChart.data.datasets[1].data = data.datasets.executor2;
        dailyProcessingChart.data.datasets[2].data = data.datasets.executor3;
        dailyProcessingChart.update();
    }
}

function updateCrosscheckerChart(data) {
    if (crosscheckerChart && data) {
        crosscheckerChart.data.labels = data.labels;
        crosscheckerChart.data.datasets[0].data = data.verified;
        crosscheckerChart.data.datasets[1].data = data.rejected;
        crosscheckerChart.update();
    }
}

function updateEditorChart(data) {
    if (editorChart && data) {
        editorChart.data.labels = data.labels;
        editorChart.data.datasets[0].data = data.completed;
        editorChart.update();
    }
}

function updateWorkflowChart(data) {
    if (workflowChart && data) {
        workflowChart.data.datasets[0].data = [
            data.pending_crosscheck,
            data.verified,
            data.rejected,
            data.pending_editing,
            data.editing_completed
        ];
        workflowChart.update();
    }
}

function refreshActivity() {
    console.log('Refreshing activity feed...');
    // In a real implementation, this would fetch new activity data
    showAlert('info', 'Activity feed refreshed');
}

// User Management Functions
function showAddUserModal() {
    currentUserId = null;
    document.getElementById('userModalTitle').innerHTML = '<i class="fas fa-user-plus me-2"></i>Add New User';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    document.getElementById('passwordHelp').style.display = 'none';
    document.getElementById('password').required = true;
    
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

function editUser(userId, username, role) {
    currentUserId = userId;
    document.getElementById('userModalTitle').innerHTML = '<i class="fas fa-user-edit me-2"></i>Edit User';
    document.getElementById('userId').value = userId;
    document.getElementById('username').value = username;
    document.getElementById('role').value = role;
    document.getElementById('password').value = '';
    document.getElementById('passwordHelp').style.display = 'block';
    document.getElementById('password').required = false;
    
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

function saveUser() {
    const userId = document.getElementById('userId').value;
    const username = document.getElementById('username').value.trim();
    const role = document.getElementById('role').value;
    const password = document.getElementById('password').value.trim();
    
    if (!username || !role) {
        alert('Username and role are required');
        return;
    }
    
    if (!userId && !password) {
        alert('Password is required for new users');
        return;
    }
    
    // Show loading state
    const saveBtn = document.getElementById('saveUserBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    saveBtn.disabled = true;
    
    const url = userId ? `/api/admin/users/${userId}` : '/api/admin/users';
    const method = userId ? 'PUT' : 'POST';
    
    const data = {
        username: username,
        role: role
    };
    
    if (password) {
        data.password = password;
    }
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();
            
            // Refresh page to update user list
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert('error', data.error || 'Error saving user');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Network error while saving user');
    })
    .finally(() => {
        // Restore button state
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

function deleteUser(userId, username) {
    if (!confirm(`Are you sure you want to delete user "${username}"?`)) {
        return;
    }
    
    fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            
            // Refresh page to update user list
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert('error', data.error || 'Error deleting user');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Network error while deleting user');
    });
}

// Records Management Functions
function loadAllRecords() {
    const recordsDiv = document.getElementById('recordsTable');
    recordsDiv.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><p class="mt-2">Loading records...</p></div>';
    
    fetch('/api/admin/metadata_records')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            allRecords = data.records;
            displayRecords(data.records);
        } else {
            recordsDiv.innerHTML = '<div class="alert alert-danger">Error loading records: ' + (data.error || 'Unknown error') + '</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        recordsDiv.innerHTML = '<div class="alert alert-danger">Network error while loading records</div>';
    });
}

function displayRecords(records) {
    const recordsDiv = document.getElementById('recordsTable');
    
    if (records.length === 0) {
        recordsDiv.innerHTML = '<div class="text-center py-4"><i class="fas fa-inbox fa-3x text-muted mb-3"></i><h5 class="text-muted">No records found</h5></div>';
        return;
    }
    
    let tableHTML = `
        <div class="table-responsive">
            <table class="table table-hover table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>Folder Name</th>
                        <th>Executor</th>
                        <th>Status</th>
                        <th>Cross-Checker</th>
                        <th>Editor</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    records.forEach(record => {
        tableHTML += `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-folder text-warning me-2"></i>
                        <div>
                            <strong>${record.folder_name || 'N/A'}</strong>
                            ${record.metadata && record.metadata.soft_renamed ?
                                `<br><small class="text-info">
                                    <i class="fas fa-tag me-1"></i>
                                    <strong>Soft Renamed:</strong> ${record.metadata.soft_renamed}
                                </small>` : ''
                            }
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-primary">${record.executor || 'N/A'}</span>
                </td>
                <td>
                    <span class="badge bg-${getStatusColor(record.status)}">${record.status || 'N/A'}</span>
                </td>
                <td>
                    ${record.crosschecker ? `<span class="badge bg-success">${record.crosschecker}</span>` : '<span class="text-muted">Pending</span>'}
                </td>
                <td>
                    ${record.editor ? `<span class="badge bg-warning">${record.editor}</span>` : '<span class="text-muted">N/A</span>'}
                </td>
                <td>
                    <small>${record.created_at || 'N/A'}</small>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="viewRecordDetails(${record.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    });
    
    tableHTML += '</tbody></table></div>';
    recordsDiv.innerHTML = tableHTML;
}

function getStatusColor(status) {
    switch(status) {
        case 'verified': return 'success';
        case 'rejected': return 'danger';
        case 'pending': return 'warning';
        default: return 'secondary';
    }
}

function viewRecordDetails(recordId) {
    const record = allRecords.find(r => r.id === recordId);
    if (!record) {
        alert('Record not found');
        return;
    }
    
    // Create a detailed view modal (simplified for now)
    alert(`Record Details:\nFolder: ${record.folder_name}\nExecutor: ${record.executor}\nStatus: ${record.status}`);
}

function exportRecords() {
    if (allRecords.length === 0) {
        alert('No records to export. Please load records first.');
        return;
    }
    
    // Convert records to CSV
    const headers = ['Folder Name', 'Executor', 'Status', 'Cross-Checker', 'Editor', 'Created'];
    const csvContent = [
        headers.join(','),
        ...allRecords.map(record => [
            record.folder_name || '',
            record.executor || '',
            record.status || '',
            record.crosschecker || '',
            record.editor || '',
            record.created_at || ''
        ].join(','))
    ].join('\n');
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'archives_mediaflow_pro_records.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    
    showAlert('success', 'Records exported successfully!');
}

function showAlert(type, message) {
    // Create and show alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at the top of the main container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
