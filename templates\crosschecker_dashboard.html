{% extends "base.html" %}

{% block title %}Cross-Checker Dashboard{% endblock %}

{% block styles %}
<style>
/* Beautiful Radio Button Destination Options */
.destination-options {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #e9ecef;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.destination-option {
    height: 100%;
}

.destination-btn {
    height: 140px;
    border: 2px solid;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.destination-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.destination-btn.btn-outline-danger:hover {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-color: #e53e3e;
}

.destination-btn.btn-outline-success:hover {
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    border-color: #38a169;
}

.destination-btn.btn-outline-warning:hover {
    background: linear-gradient(135deg, #fffbf0 0%, #faf089 100%);
    border-color: #d69e2e;
}

.btn-check:checked + .destination-btn {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    z-index: 10;
}

.btn-check:checked + .btn-outline-danger {
    background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
    border-color: #e53e3e;
    color: #742a2a;
}

.btn-check:checked + .btn-outline-success {
    background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
    border-color: #38a169;
    color: #22543d;
}

.btn-check:checked + .btn-outline-warning {
    background: linear-gradient(135deg, #faf089 0%, #f6e05e 100%);
    border-color: #d69e2e;
    color: #744210;
}

.destination-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 10px;
}

.destination-icon {
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.destination-btn:hover .destination-icon {
    transform: scale(1.1);
}

.btn-check:checked + .destination-btn .destination-icon {
    transform: scale(1.2);
    animation: pulse 0.6s ease;
}

@keyframes pulse {
    0% { transform: scale(1.2); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1.2); }
}

.destination-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    line-height: 1.2;
}

.destination-path {
    font-size: 11px;
    font-family: 'Courier New', monospace;
    opacity: 0.8;
    line-height: 1.1;
}

.selected-destination {
    animation: fadeInUp 0.4s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.destination-options .badge {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .destination-btn {
        height: 120px;
    }

    .destination-title {
        font-size: 13px;
    }

    .destination-path {
        font-size: 10px;
    }

    .destination-icon i {
        font-size: 1.5rem !important;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-check-double text-primary me-2"></i>Cross-Checker Dashboard
                    </h2>
                    <p class="text-muted mb-0">Review and validate processed folders</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Pending Verification
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_verifications|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Verified Today
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Processed
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-folder fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Avg. Processing Time
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">--</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-stopwatch fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Verifications Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>Folders Pending Verification
                    </h6>
                </div>
                <div class="card-body">
                    {% if pending_verifications %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Folder Name</th>
                                    <th>Folder Path</th>
                                    <th>Executor</th>
                                    <th>Processed At</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in pending_verifications %}
                                <tr>
                                    <td>
                                        <strong>{{ item[1] }}</strong>
                                    </td>
                                    <td>
                                        <code>{{ item[2] }}</code>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ item[5] }}</span>
                                    </td>
                                    <td>
                                        <small>{{ item[4] }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">Pending Verification</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-success review-btn"
                                                    data-processing-id="{{ item[0] }}"
                                                    data-folder-name="{{ item[1] }}"
                                                    data-folder-path="{{ item[2] }}"
                                                    data-metadata="{{ item[3] }}"
                                                    data-folder="{{ item[1] }}"
                                                    title="Review Metadata">
                                                <i class="fas fa-eye me-1"></i>Review
                                            </button>
                                            <button class="btn btn-sm btn-primary validate-btn"
                                                    data-processing-id="{{ item[0] }}"
                                                    data-folder-name="{{ item[1] }}"
                                                    data-folder-path="{{ item[2] }}"
                                                    data-metadata="{{ item[3] }}"
                                                    data-folder="{{ item[1] }}"
                                                    title="Validate & Process">
                                                <i class="fas fa-check me-1"></i>Validate
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-btn"
                                                    data-processing-id="{{ item[0] }}"
                                                    data-folder-name="{{ item[1] }}"
                                                    data-folder-path="{{ item[2] }}"
                                                    data-folder="{{ item[1] }}"
                                                    title="Delete Folder">
                                                <i class="fas fa-trash me-1"></i>Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No folders pending verification</h5>
                        <p class="text-muted">All processed folders have been verified.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Review Metadata Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>Review Metadata
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="metadataReview">
                    <!-- Metadata content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Validation Workflow Modal -->
<div class="modal fade" id="validationWorkflowModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-double me-2"></i>Validation Workflow
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>Metadata Review
                        </h6>
                        <div id="metadataReviewContent">
                            <!-- Complete editable metadata review content will be populated by JavaScript -->
                        </div>

                        <!-- Validation Status -->
                        <div class="mt-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-check-circle me-2"></i>Validation Status
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="validationStatus" class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Review all metadata fields above. Click on any field to edit. Changes are auto-saved.
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-save me-1"></i>Auto-save: <span id="autoSaveStatus">Ready</span>
                                            </small>
                                        </div>
                                        <div>
                                            <button class="btn btn-outline-primary btn-sm" id="validateAllFieldsBtn">
                                                <i class="fas fa-check-double me-1"></i>Validate All Fields
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-cogs me-2"></i>Validation Settings
                        </h6>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Server-Side File Browsing:</strong> Browse the server's file system to select files and folders.
                            You can navigate through drives (C:, D:, E:) and folders using the visual browser interface.
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Path Entry:</strong> You can type/paste full file paths directly in the fields below,
                            or use the "Browse System" buttons to select files/folders visually.
                        </div>

                        <!-- Source Video File Selection -->
                        <div class="mb-4">
                            <label for="movFilePath" class="form-label fw-bold">
                                <i class="fas fa-video text-info me-1"></i>Source Video File (.mov)
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="movFilePath" placeholder="Enter full path to video file or click Browse" title="Select the video file for audio extraction">
                                <button class="btn btn-outline-info" type="button" id="browseMovFile" title="Browse system for video file">
                                    <i class="fas fa-hdd me-1"></i>Browse System
                                </button>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>Select the video file (.mov, .mp4, .avi) for audio extraction
                            </small>
                        </div>

                        <!-- Audio Output Destination - Fixed Path -->
                        <div class="mb-4">
                            <label for="audioOutputPath" class="form-label fw-bold">
                                <i class="fas fa-music text-success me-1"></i>Audio Extraction Destination
                                <span class="badge bg-success ms-2">Fixed Path</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="fas fa-lock text-muted"></i>
                                </span>
                                <input type="text" class="form-control bg-light" id="audioOutputPath"
                                       value="E:\Extracted Audios"
                                       readonly
                                       title="Fixed destination path for all extracted audio files">
                                <span class="input-group-text bg-success text-white">
                                    <i class="fas fa-check"></i>
                                </span>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>All extracted audio files are automatically saved to this fixed location for consistency
                            </small>
                        </div>

                        <!-- Source Folder Selection -->
                        <div class="mb-4">
                            <label for="sourceFolderPath" class="form-label fw-bold">
                                <i class="fas fa-folder-open text-warning me-1"></i>Source Folder (Folder to Move)
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="sourceFolderPath" placeholder="Enter full path to source folder or click Browse" title="Select the folder that will be moved">
                                <button class="btn btn-outline-warning" type="button" id="browseSourceFolder" title="Browse system for source folder">
                                    <i class="fas fa-hdd me-1"></i>Browse System
                                </button>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>Select the project folder that will be moved (usually contains the video files)
                            </small>
                        </div>

                        <!-- Destination Folder Selection - Radio Buttons -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-folder-plus text-primary me-1"></i>Destination Folder (Where to Move)
                                <span class="badge bg-primary ms-2">Select One</span>
                            </label>

                            <!-- Radio Button Options -->
                            <div class="destination-options mt-3">
                                <div class="row g-3">
                                    <!-- To Be Deleted Option -->
                                    <div class="col-md-4">
                                        <div class="destination-option">
                                            <input type="radio" class="btn-check" name="destinationOption" id="toBeDeleted" value="E:\To Be Deleted" autocomplete="off">
                                            <label class="btn btn-outline-danger destination-btn w-100" for="toBeDeleted">
                                                <div class="destination-content">
                                                    <div class="destination-icon">
                                                        <i class="fas fa-trash-alt fa-2x text-danger"></i>
                                                    </div>
                                                    <div class="destination-title">To Be Deleted</div>
                                                    <div class="destination-path">E:\To Be Deleted</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- To Be Ingested Option -->
                                    <div class="col-md-4">
                                        <div class="destination-option">
                                            <input type="radio" class="btn-check" name="destinationOption" id="toBeIngested" value="E:\To Be Ingested" autocomplete="off">
                                            <label class="btn btn-outline-success destination-btn w-100" for="toBeIngested">
                                                <div class="destination-content">
                                                    <div class="destination-icon">
                                                        <i class="fas fa-download fa-2x text-success"></i>
                                                    </div>
                                                    <div class="destination-title">To Be Ingested</div>
                                                    <div class="destination-path">E:\To Be Ingested</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Sent To Editor Option -->
                                    <div class="col-md-4">
                                        <div class="destination-option">
                                            <input type="radio" class="btn-check" name="destinationOption" id="sentToEditor" value="E:\For Editing" autocomplete="off">
                                            <label class="btn btn-outline-warning destination-btn w-100" for="sentToEditor">
                                                <div class="destination-content">
                                                    <div class="destination-icon">
                                                        <i class="fas fa-edit fa-2x text-warning"></i>
                                                    </div>
                                                    <div class="destination-title">Sent To Editor</div>
                                                    <div class="destination-path">E:\For Editing</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Hidden input to store the selected path -->
                            <input type="hidden" id="finalFolderPath" value="">

                            <!-- Selected Path Display -->
                            <div class="selected-destination mt-3 d-none">
                                <div class="alert alert-info border-start border-primary border-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-check-circle text-primary me-2"></i>
                                        <div>
                                            <strong>Selected Destination:</strong>
                                            <span id="selectedDestinationPath" class="font-monospace ms-2"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>Select where the source folder will be moved and renamed with the soft-renamed name
                            </small>
                        </div>

                        <!-- Processing Steps Preview -->
                        <div class="mb-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-muted mb-2">
                                        <i class="fas fa-list-ol me-1"></i>Processing Steps:
                                    </h6>
                                    <ol class="mb-0 small text-muted">
                                        <li><i class="fas fa-music text-info me-1"></i>Extract audio from selected video file</li>
                                        <li><i class="fas fa-edit text-warning me-1"></i>Rename audio file using metadata and save to audio destination</li>
                                        <li><i class="fas fa-folder-open text-primary me-1"></i>Move source folder to destination and rename with soft-renamed name</li>
                                        <li><i class="fas fa-table text-success me-1"></i>Update Google Sheets with processing details</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <!-- Soft Rename Preview -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tag text-primary me-1"></i>Soft Rename Preview
                            </label>
                            <div class="card bg-light">
                                <div class="card-body p-2">
                                    <small class="text-muted">Original:</small>
                                    <div id="originalFolderName" class="fw-bold text-dark">-</div>
                                    <small class="text-muted mt-1">Renamed to:</small>
                                    <div id="softRenamedPreview" class="fw-bold text-primary">-</div>
                                </div>
                            </div>
                        </div>

                        <!-- Editing Required -->
                        <div class="mb-4">
                            <div class="card border-info">
                                <div class="card-body p-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="editingRequired">
                                        <label class="form-check-label fw-bold" for="editingRequired">
                                            <i class="fas fa-edit text-info me-1"></i>Requires Editing
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">If checked, folder will be queued for Editor after validation</small>
                                </div>
                            </div>
                        </div>

                        <!-- Validation Notes -->
                        <div class="mb-3">
                            <label for="validationNotes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note text-secondary me-1"></i>Validation Notes
                            </label>
                            <textarea class="form-control" id="validationNotes" rows="3" placeholder="Add validation notes and comments..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-info" id="addToQueueBtn">
                    <i class="fas fa-plus me-1"></i>Add to Processing Queue
                </button>
                <button type="button" class="btn btn-danger" id="rejectWorkflowBtn">
                    <i class="fas fa-times me-1"></i>Reject
                </button>
                <button type="button" class="btn btn-success" id="approveWorkflowBtn">
                    <i class="fas fa-check me-1"></i>Approve & Process Now
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Batch Processing Queue Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 bg-info text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-list-ol me-2"></i>Audio Processing Queue
                    </h6>
                    <div>
                        <button class="btn btn-light btn-sm me-2" id="clearQueueBtn">
                            <i class="fas fa-trash me-1"></i>Clear Queue
                        </button>
                        <button class="btn btn-success btn-sm" id="startBatchProcessingBtn" disabled>
                            <i class="fas fa-play me-1"></i>Start Processing (<span id="queueCount">0</span> items)
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="processingQueue">
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No items in processing queue</h5>
                        <p class="text-muted">Add folders to the queue using the "Add to Processing Queue" button in the validation modal.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Live Operation Logs Section -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 bg-dark text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-terminal me-2"></i>Live Operation Logs
                    </h6>
                    <div>
                        <button class="btn btn-outline-light btn-sm me-2" id="clearLogsBtn">
                            <i class="fas fa-eraser me-1"></i>Clear Logs
                        </button>
                        <button class="btn btn-outline-light btn-sm" id="downloadLogsBtn">
                            <i class="fas fa-download me-1"></i>Download Logs
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="operationLogs" class="bg-dark text-light p-3" style="height: 300px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 14px;">
                    <div class="text-muted">
                        <i class="fas fa-info-circle me-2"></i>Operation logs will appear here in real-time...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-cogs me-2"></i>Audio Processing in Progress
                </h5>
            </div>
            <div class="modal-body">
                <div class="mb-4">
                    <h6 class="text-primary">Current Operation:</h6>
                    <div id="currentOperation" class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>Initializing...
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary">Overall Progress:</h6>
                    <div class="progress mb-2" style="height: 25px;">
                        <div id="overallProgress" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            0%
                        </div>
                    </div>
                    <small class="text-muted">Processing <span id="currentItem">0</span> of <span id="totalItems">0</span> items</small>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary">Step Progress:</h6>
                    <div class="progress mb-2" style="height: 20px;">
                        <div id="stepProgress" class="progress-bar bg-success"
                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            0%
                        </div>
                    </div>
                    <small class="text-muted" id="stepDescription">Ready to start...</small>
                </div>

                <div class="mb-3">
                    <h6 class="text-primary">Processing Steps:</h6>
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center" id="step1">
                            <span><i class="fas fa-video me-2"></i>Audio Extraction (.mov → .wav)</span>
                            <span class="badge bg-secondary">Pending</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center" id="step2">
                            <span><i class="fas fa-file-signature me-2"></i>File Renaming (AudioCode_NewName)</span>
                            <span class="badge bg-secondary">Pending</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center" id="step3">
                            <span><i class="fas fa-folder-open me-2"></i>Folder Movement & Renaming</span>
                            <span class="badge bg-secondary">Pending</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center" id="step4">
                            <span><i class="fas fa-table me-2"></i>Google Sheets Update</span>
                            <span class="badge bg-secondary">Pending</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" id="cancelProcessingBtn">
                    <i class="fas fa-stop me-1"></i>Cancel Processing
                </button>
            </div>
        </div>
    </div>
</div>

<!-- File Browser Modal removed - now using native system dialogs like executors -->
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='crosschecker.js') }}?v=20250705"></script>
<script>
// Debug: Verify functions are loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Cross-Checker Debug: Checking function availability...');
    console.log('✅ createEditableField:', typeof createEditableField);
    console.log('✅ editField:', typeof editField);
    console.log('✅ saveField:', typeof saveField);

    // Add visual indicator that functions are loaded
    if (typeof createEditableField === 'function' &&
        typeof editField === 'function' &&
        typeof saveField === 'function') {
        console.log('🎉 All editable field functions loaded successfully!');
    } else {
        console.error('❌ Some editable field functions are missing!');
    }
});
</script>
{% endblock %}
