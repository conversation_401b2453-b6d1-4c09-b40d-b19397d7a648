// Cross-Checker Dashboard JavaScript - COMPLETE FUNCTIONAL IMPLEMENTATION

// Global variables
let currentProcessingId = null;
let currentFolderName = null;
let currentMetadata = null;

// Batch processing variables
let processingQueue = [];
let isProcessing = false;
let currentProcessingIndex = 0;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Cross-Checker Dashboard - DOM Loaded');
    initializeCrossChecker();
});

function initializeCrossChecker() {
    console.log('🔧 Initializing Cross-Checker Dashboard...');
    
    // Setup button event listeners
    setupButtonEventListeners();
    
    // Test Bootstrap
    if (typeof bootstrap !== 'undefined') {
        console.log('✅ Bootstrap loaded');
    } else {
        console.error('❌ Bootstrap NOT loaded');
        showAlert('error', 'Bootstrap not loaded - modals may not work');
    }
    
    console.log('✅ Cross-Checker Dashboard initialized');
}

function setupButtonEventListeners() {
    console.log('🔧 Setting up button event listeners...');
    
    // Review buttons
    const reviewButtons = document.querySelectorAll('.review-btn');
    reviewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🔍 Review button clicked');
            
            const processingId = this.getAttribute('data-processing-id');
            const folderName = this.getAttribute('data-folder-name');
            const metadata = this.getAttribute('data-metadata');
            
            reviewMetadata(processingId, folderName, metadata);
        });
    });
    
    // Validate buttons
    const validateButtons = document.querySelectorAll('.validate-btn');
    validateButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🔧 Validate button clicked');
            
            const processingId = this.getAttribute('data-processing-id');
            const folderName = this.getAttribute('data-folder-name');
            const metadata = this.getAttribute('data-metadata');
            
            openValidationWorkflow(processingId, folderName, metadata);
        });
    });
    
    // Delete buttons
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🗑️ Delete button clicked');
            
            const processingId = this.getAttribute('data-processing-id');
            const folderName = this.getAttribute('data-folder-name');
            
            confirmDeleteFolder(processingId, folderName);
        });
    });
    
    console.log(`✅ Event listeners added: ${reviewButtons.length} review, ${validateButtons.length} validate, ${deleteButtons.length} delete`);
}

function reviewMetadata(processingId, folderName, metadataJson) {
    console.log('🔍 Opening review modal for:', folderName);
    
    try {
        // Parse metadata
        const metadata = typeof metadataJson === 'string' ? JSON.parse(metadataJson || '{}') : (metadataJson || {});
        
        // Store current data
        currentProcessingId = processingId;
        currentFolderName = folderName;
        currentMetadata = metadata;
        
        // Create review HTML
        const reviewHTML = createMetadataReviewHTML(metadata, folderName);
        
        // Insert into modal
        const reviewElement = document.getElementById('metadataReview');
        if (reviewElement) {
            reviewElement.innerHTML = reviewHTML;
        } else {
            console.error('❌ metadataReview element not found');
            showAlert('error', 'Review modal content area not found');
            return;
        }
        
        // Show modal
        const modalElement = document.getElementById('reviewModal');
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('✅ Review modal shown');
            showAlert('success', 'Metadata loaded for review');
        } else {
            console.error('❌ reviewModal not found');
            showAlert('error', 'Review modal not found');
        }
        
    } catch (error) {
        console.error('❌ Error in reviewMetadata:', error);
        showAlert('error', 'Error loading metadata: ' + error.message);
    }
}

function openValidationWorkflow(processingId, folderName, metadataJson) {
    console.log('🔧 Opening validation workflow for:', folderName);
    
    try {
        // Parse metadata
        const metadata = typeof metadataJson === 'string' ? JSON.parse(metadataJson || '{}') : (metadataJson || {});
        
        // Store current data
        currentProcessingId = processingId;
        currentFolderName = folderName;
        currentMetadata = metadata;
        
        // Create review HTML
        const reviewHTML = createMetadataReviewHTML(metadata, folderName);
        
        // Insert into validation modal
        const reviewElement = document.getElementById('metadataReviewContent');
        if (reviewElement) {
            reviewElement.innerHTML = reviewHTML;
        } else {
            console.error('❌ metadataReviewContent element not found');
            showAlert('error', 'Validation modal content area not found');
            return;
        }
        
        // Pre-populate file paths
        populateFilePaths(folderName, metadata);
        
        // Setup validation buttons
        setupValidationButtons();
        
        // Show modal
        const modalElement = document.getElementById('validationWorkflowModal');
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('✅ Validation workflow modal shown');
            showAlert('success', 'Validation workflow opened');
        } else {
            console.error('❌ validationWorkflowModal not found');
            showAlert('error', 'Validation workflow modal not found');
        }
        
    } catch (error) {
        console.error('❌ Error in openValidationWorkflow:', error);
        showAlert('error', 'Error opening validation workflow: ' + error.message);
    }
}

function confirmDeleteFolder(processingId, folderName) {
    console.log('🗑️ Confirming delete for:', folderName);
    
    if (confirm(`Are you sure you want to delete the folder "${folderName}"?\n\nThis action cannot be undone.`)) {
        deleteFolder(processingId, folderName);
    }
}

function deleteFolder(processingId, folderName) {
    console.log('🗑️ Deleting folder:', folderName);
    
    showAlert('info', 'Deleting folder...');
    
    fetch(`/api/delete_processed_folder/${processingId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', `Folder "${folderName}" deleted successfully!`);
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showAlert('error', 'Error deleting folder: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('❌ Error deleting folder:', error);
        showAlert('error', 'Network error while deleting folder');
    });
}

function createMetadataReviewHTML(metadata, folderName) {
    // Store current metadata for editing
    window.currentEditableMetadata = JSON.parse(JSON.stringify(metadata));

    return `
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title mb-2">
                            <i class="fas fa-folder me-2"></i>${folderName}
                        </h5>
                        <p class="card-text mb-0">
                            <i class="fas fa-edit me-2"></i><strong>COMPLETE EDITABLE METADATA REVIEW</strong> - Click any field to edit
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- General Fields Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>🔊 General Fields
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                ${createEditableFieldWithButton('audio_code', 'Audio Code', metadata.audio_code, 'text', 'fas fa-music text-success')}
                                ${createEditableField('transcription_status', 'Transcription Status', metadata.transcription_status, 'select', 'fas fa-file-alt text-primary', ['Applicable', 'Already in Social Media', 'Not Applicable', 'MSR VT Video'])}
                                ${createEditableField('soft_renamed', 'Soft Renamed Name', metadata.soft_renamed, 'text', 'fas fa-tag text-info')}
                                ${createEditableField('backup_type', 'Backup Type', metadata.backup_type, 'select', 'fas fa-archive text-warning', ['Stems', 'Consolidated', 'Unconsolidated', 'Mov', 'Mp4', 'Trimmed', 'Premiere-Pro-Transcoded', 'Edited-DVD', 'Premiere-Pro-Trimmed', 'Not Applicable', 'Pending', 'Edited', 'SDI-Digitized_Edited', 'Unpublished', 'Avi', 'Mxf', 'Wav'])}
                            </div>
                            <div class="col-md-6">
                                ${createEditableField('filename', 'Filename', metadata.filename || folderName, 'text', 'fas fa-file text-primary')}
                                ${createEditableField('transcription', 'Transcription', metadata.transcription, 'textarea', 'fas fa-file-alt text-secondary')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Information Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-tags me-2"></i>🏷️ Content Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                ${createEditableField('content_tags', 'Content Tags', (metadata.content_tags || []).join(', '), 'tags', 'fas fa-hashtag text-primary')}
                                ${createEditableField('ocd_number', 'OCD Number', metadata.ocd_number, 'text', 'fas fa-barcode text-dark')}
                                ${createEditableField('video_type', 'Video Type', metadata.video_type, 'select', 'fas fa-video text-danger', ['Talk', 'Documentary', 'Insta-Reels', 'Class', 'Interview', 'Promo', 'Daily-Mystic-Quote', 'Episode', 'Q-And-A', 'Message', 'Intro', 'Song', 'Glimpses', 'Animation', 'Sharings', 'Video-Book', 'Teaser', 'Poem', 'Telefilm', 'Non-Ashram Videos', 'Event', 'Miscellaneous'])}
                                ${createEditableField('title', 'Title', metadata.title, 'text', 'fas fa-heading text-primary')}
                                ${createEditableField('description', 'Description', metadata.description, 'textarea', 'fas fa-align-left text-secondary')}
                            </div>
                            <div class="col-md-6">
                                ${createEditableField('component', 'Component', metadata.component, 'select', 'fas fa-puzzle-piece text-success', ['Sadhguru', 'Non-Sadhguru'])}
                                ${createEditableField('language', 'Language', metadata.language, 'select', 'fas fa-globe text-info', ['English', 'Hindi', 'Tamil', 'Telugu', 'Spanish', 'French'])}
                                ${createEditableFieldWithButton('video_id', 'Video ID', metadata.video_id, 'text', 'fas fa-id-card text-warning')}
                                ${createEditableField('url', 'URL', metadata.url, 'url', 'fas fa-link text-info')}
                                ${createEditableField('is_social_media', 'Is Social Media?', metadata.is_social_media || false, 'toggle', 'fas fa-share-alt text-primary')}
                            </div>
                        </div>

                        <!-- Additional Project Information -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                ${createEditableField('platform', 'Publish Platform', metadata.platform, 'select', 'fas fa-tv text-primary', ['YouTube', 'Instagram', 'Facebook', 'Twitter', 'TikTok', 'LinkedIn', 'Internal Archive'])}
                                ${createEditableField('duration', 'Duration', metadata.duration, 'text', 'fas fa-clock text-warning')}
                                ${createEditableField('total_duration', 'Total Duration', metadata.total_duration, 'text', 'fas fa-stopwatch text-info')}
                            </div>
                            <div class="col-md-6">
                                ${createEditableField('project_name', 'Project Name', metadata.project_name, 'text', 'fas fa-project-diagram text-success')}
                                ${createEditableField('project_date', 'Project Date', metadata.project_date, 'date', 'fas fa-calendar-alt text-primary')}
                                ${createEditableField('publish_date', 'Publish Date', metadata.publish_date, 'date', 'fas fa-calendar-check text-info')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Media Metadata Section -->
        <div class="row mb-4" id="socialMediaSection" style="display: ${metadata.is_social_media ? 'block' : 'none'}">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>📹 Social Media Metadata
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                ${createEditableField('video_id', 'Video ID', metadata.video_id, 'text', 'fas fa-id-card text-primary')}
                                ${createEditableField('post_date', 'Post Date', metadata.post_date || metadata.project_date, 'date', 'fas fa-calendar text-info')}
                                ${createEditableField('original_url', 'Original URL', metadata.original_url || metadata.url, 'url', 'fas fa-link text-success')}
                            </div>
                            <div class="col-md-6">
                                ${createEditableField('duration', 'Duration', metadata.duration || metadata.total_duration, 'text', 'fas fa-clock text-warning')}
                                ${createEditableField('platform_name', 'Platform Name', metadata.platform_name || metadata.platform, 'select', 'fas fa-tv text-danger', ['YouTube', 'Instagram', 'Facebook', 'Twitter', 'TikTok', 'LinkedIn'])}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Content Details Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-secondary">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-list me-2"></i>📝 Additional Details
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                ${createEditableField('title', 'Title', metadata.title, 'text', 'fas fa-heading text-primary')}
                                ${createEditableField('description', 'Description', metadata.description, 'textarea', 'fas fa-align-left text-info')}
                            </div>
                            <div class="col-md-6">
                                ${createEditableField('distribution_type', 'Distribution Type', metadata.distribution_type, 'select', 'fas fa-share text-success', ['Internal', 'Social Media'])}
                                ${createEditableField('with_logo', 'With Logo', metadata.with_logo, 'select', 'fas fa-image text-primary', ['Yes', 'No'])}
                                ${createEditableField('influencer', 'Influencer', metadata.influencer, 'select', 'fas fa-star text-warning', ['Yes', 'No'])}
                                ${createEditableField('additional_notes', 'Additional Notes', metadata.additional_notes, 'textarea', 'fas fa-sticky-note text-secondary')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createEditableField(fieldName, label, value, type, iconClass, options = []) {
    const fieldId = `edit_${fieldName}`;
    const displayValue = value || 'Click to add...';
    const isEmpty = !value || value === '';

    let inputHTML = '';

    switch (type) {
        case 'text':
        case 'url':
        case 'date':
            inputHTML = `
                <input type="${type}" class="form-control editable-input" id="${fieldId}"
                       value="${value || ''}" data-field="${fieldName}"
                       style="display: none;" onblur="saveField('${fieldName}')"
                       onkeypress="handleEnterKey(event, '${fieldName}')">
            `;
            break;

        case 'textarea':
            inputHTML = `
                <textarea class="form-control editable-input" id="${fieldId}"
                          data-field="${fieldName}" rows="3" style="display: none;"
                          onblur="saveField('${fieldName}')">${value || ''}</textarea>
            `;
            break;

        case 'select':
            const optionsHTML = options.map(opt =>
                `<option value="${opt}" ${opt === value ? 'selected' : ''}>${opt}</option>`
            ).join('');
            inputHTML = `
                <select class="form-select editable-input" id="${fieldId}"
                        data-field="${fieldName}" style="display: none;"
                        onchange="saveField('${fieldName}')">
                    <option value="">Select ${label}</option>
                    ${optionsHTML}
                </select>
            `;
            break;

        case 'toggle':
            const isChecked = value === true || value === 'true' || value === 'yes';
            inputHTML = `
                <div class="form-check form-switch">
                    <input class="form-check-input editable-input" type="checkbox" id="${fieldId}"
                           data-field="${fieldName}" ${isChecked ? 'checked' : ''}
                           onchange="saveField('${fieldName}')">
                    <label class="form-check-label" for="${fieldId}">
                        ${isChecked ? 'Yes' : 'No'}
                    </label>
                </div>
            `;
            break;

        case 'tags':
            inputHTML = `
                <input type="text" class="form-control editable-input" id="${fieldId}"
                       value="${value || ''}" data-field="${fieldName}"
                       placeholder="Enter tags separated by commas" style="display: none;"
                       onblur="saveField('${fieldName}')"
                       onkeypress="handleEnterKey(event, '${fieldName}')">
            `;
            break;
    }

    if (type === 'toggle') {
        return `
            <div class="mb-3">
                <label class="form-label fw-bold">
                    <i class="${iconClass} me-2"></i>${label}:
                </label>
                <div class="editable-field-container">
                    ${inputHTML}
                </div>
            </div>
        `;
    }

    return `
        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="${iconClass} me-2"></i>${label}:
                <button class="btn btn-sm btn-outline-primary ms-2 edit-btn" onclick="editField('${fieldName}')" title="Click to edit">
                    <i class="fas fa-edit"></i>
                </button>
            </label>
            <div class="editable-field-container">
                <div class="form-control-plaintext border rounded p-2 bg-light editable-display ${isEmpty ? 'text-muted' : ''}"
                     id="display_${fieldName}" onclick="editField('${fieldName}')" style="cursor: pointer;">
                    ${displayValue}
                </div>
                ${inputHTML}
            </div>
        </div>
    `;
}

function createEditableFieldWithButton(fieldName, label, value, type, iconClass, options = []) {
    const fieldId = `edit_${fieldName}`;
    const displayValue = value || 'Click to add...';
    const isEmpty = !value || value === '';

    let inputHTML = '';

    switch (type) {
        case 'text':
        case 'url':
        case 'date':
            inputHTML = `
                <input type="${type}" class="form-control editable-input" id="${fieldId}"
                       value="${value || ''}" data-field="${fieldName}"
                       style="display: none;" onblur="saveField('${fieldName}')"
                       onkeypress="handleEnterKey(event, '${fieldName}')">
            `;
            break;

        case 'textarea':
            inputHTML = `
                <textarea class="form-control editable-input" id="${fieldId}"
                          data-field="${fieldName}" rows="3" style="display: none;"
                          onblur="saveField('${fieldName}')">${value || ''}</textarea>
            `;
            break;

        case 'select':
            const optionsHTML = options.map(opt =>
                `<option value="${opt}" ${opt === value ? 'selected' : ''}>${opt}</option>`
            ).join('');
            inputHTML = `
                <select class="form-select editable-input" id="${fieldId}"
                        data-field="${fieldName}" style="display: none;"
                        onchange="saveField('${fieldName}')">
                    <option value="">Select ${label}</option>
                    ${optionsHTML}
                </select>
            `;
            break;
    }

    return `
        <div class="mb-3">
            <label class="form-label">
                <i class="${iconClass} me-2"></i>${label}
            </label>
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="editable-field-display ${isEmpty ? 'text-muted' : ''}"
                         id="display_${fieldName}" onclick="editField('${fieldName}')"
                         style="cursor: pointer; padding: 8px; border: 1px solid #dee2e6; border-radius: 0.375rem; background-color: #f8f9fa;">
                        ${displayValue}
                    </div>
                    ${inputHTML}
                </div>
                ${fieldName === 'audio_code' ? `
                <button class="btn btn-outline-warning ms-2" type="button" onclick="releaseAudioCode('${value}')"
                        id="releaseCodeBtn" ${!value ? 'disabled' : ''}>
                    <i class="fas fa-unlock me-1"></i>Release Code
                </button>
                ` : ''}
                ${fieldName === 'video_id' ? `
                <button class="btn btn-outline-primary ms-2" type="button" onclick="searchGoogleSheetsInCrossChecker()"
                        id="searchGoogleSheetsBtn">
                    <i class="fas fa-search me-1"></i>Search in Google Sheets
                </button>
                ` : ''}
            </div>
        </div>
    `;
}

function populateFilePaths(folderName, metadata) {
    console.log('📁 Populating file paths for:', folderName);

    try {
        // MOV file path
        const movFileInput = document.getElementById('movFilePath');
        if (movFileInput) {
            movFileInput.value = `/complete/${folderName}/${folderName}.mov`;
        }

        // Audio output path - Fixed to E:\Extracted Audios
        const audioOutputInput = document.getElementById('audioOutputPath');
        if (audioOutputInput) {
            audioOutputInput.value = 'E:\\Extracted Audios';
            audioOutputInput.readOnly = true;
            audioOutputInput.classList.add('bg-light');
        }

        // Final folder path
        const finalFolderInput = document.getElementById('finalFolderPath');
        if (finalFolderInput) {
            finalFolderInput.value = `/final/`;
        }

        // Update soft rename preview
        updateSoftRenamePreview(folderName, metadata);

        // Setup file browser buttons
        setupFileBrowserButtons();

        // Setup destination folder radio buttons
        setupDestinationRadioButtons();

        console.log('✅ File paths populated');
    } catch (error) {
        console.error('❌ Error populating file paths:', error);
    }
}

// Setup destination folder radio buttons
function setupDestinationRadioButtons() {
    console.log('🎯 Setting up destination folder radio buttons...');

    try {
        const radioButtons = document.querySelectorAll('input[name="destinationOption"]');
        const hiddenField = document.getElementById('finalFolderPath');
        const selectedDisplay = document.querySelector('.selected-destination');
        const selectedPath = document.getElementById('selectedDestinationPath');

        if (!radioButtons.length || !hiddenField) {
            console.log('⚠️ Destination radio buttons or hidden field not found');
            return;
        }

        // Add event listeners to all radio buttons
        radioButtons.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    const selectedValue = this.value;
                    const selectedLabel = this.nextElementSibling.querySelector('.destination-title').textContent;

                    console.log(`🎯 Destination selected: ${selectedLabel} → ${selectedValue}`);

                    // Update hidden field
                    hiddenField.value = selectedValue;

                    // Show selected destination display
                    if (selectedDisplay && selectedPath) {
                        selectedPath.textContent = selectedValue;
                        selectedDisplay.classList.remove('d-none');
                    }

                    // Add visual feedback
                    showAlert('success', `Destination set: ${selectedLabel}`);

                    // Update validation button state
                    updateValidationButtonState();

                    // Ensure directories exist
                    ensureDestinationDirectory(selectedValue);
                }
            });
        });

        // Always ensure a default selection
        const checkedRadio = document.querySelector('input[name="destinationOption"]:checked');
        if (!checkedRadio && radioButtons.length > 0) {
            // Default to "To Be Ingested" option
            const defaultOption = document.getElementById('toBeIngested');
            if (defaultOption) {
                defaultOption.checked = true;
                defaultOption.dispatchEvent(new Event('change'));
                console.log('🎯 Default destination set to: To Be Ingested → E:\\To Be Ingested');
            }
        } else if (checkedRadio) {
            // Trigger change event on already selected radio to ensure hidden field is populated
            checkedRadio.dispatchEvent(new Event('change'));
            console.log('🎯 Using existing selection: ' + checkedRadio.value);
        }

        console.log('✅ Destination radio buttons setup complete');

    } catch (error) {
        console.error('❌ Error setting up destination radio buttons:', error);
    }
}

// Ensure destination directory exists (client-side validation)
function ensureDestinationDirectory(path) {
    console.log(`📁 Ensuring destination directory exists: ${path}`);

    // This is mainly for logging - actual directory creation happens server-side
    // But we can add client-side validation here if needed

    if (!path || !path.startsWith('E:\\')) {
        console.warn('⚠️ Invalid destination path:', path);
        showAlert('warning', 'Invalid destination path selected');
        return false;
    }

    console.log('✅ Destination path validated:', path);
    return true;
}

function updateSoftRenamePreview(folderName, metadata) {
    const originalElement = document.getElementById('originalFolderName');
    const renamedElement = document.getElementById('softRenamedPreview');

    if (originalElement) {
        originalElement.textContent = folderName;
    }

    if (renamedElement) {
        const softRenamed = metadata.soft_renamed || `${folderName}_renamed`;
        renamedElement.textContent = softRenamed;
    }
}

function setupFileBrowserButtons() {
    console.log('🔧 Setting up system-wide folder browser buttons...');

    // Browse MOV file button - for selecting video files with server-side browser
    const browseMovBtn = document.getElementById('browseMovFile');
    if (browseMovBtn) {
        browseMovBtn.onclick = () => openServerFileBrowser('file', 'mov');
    }

    // Browse audio output button - REMOVED (now using fixed path E:\Extracted Audios)
    // const browseAudioBtn = document.getElementById('browseAudioOutput');
    // Audio extraction destination is now fixed to E:\Extracted Audios

    // Browse source folder button - for selecting source folder with server-side browser
    const browseSourceBtn = document.getElementById('browseSourceFolder');
    if (browseSourceBtn) {
        browseSourceBtn.onclick = () => openServerFileBrowser('folder', 'source');
    }

    // Browse destination folder button - REMOVED (now using radio button selection)
    // const browseDestinationBtn = document.getElementById('browseDestinationFolder');
    // Destination folder is now selected via radio buttons with fixed paths

    console.log('✅ Server-side file browser buttons set up');
}

// Hybrid browser functions removed - using server-side browser instead

// Hybrid browser functions removed - using server-side browser

function showFilePathModal(file, targetType) {
    const modalId = 'filePathModal';

    // Remove existing modal
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const modalHTML = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-video me-2"></i>Enter Video File Path
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Selected File:</strong> ${file.name} (${formatFileSize(file.size)})
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Security Note:</strong> For security reasons, browsers don't provide the full file path.
                            Please enter the complete path to this file on your computer.
                        </div>
                        <div class="mb-3">
                            <label for="filePathInput" class="form-label">
                                <strong>Full File Path:</strong>
                            </label>
                            <input type="text" class="form-control" id="filePathInput"
                                   placeholder="e.g., C:\\Users\\<USER>\\Videos\\${file.name}"
                                   title="Enter the complete path to the selected file">
                            <div class="form-text">
                                Example: C:\\Users\\<USER>\\Documents\\Videos\\${file.name}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="confirmFilePath('${targetType}')">
                            <i class="fas fa-check me-1"></i>Confirm Path
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    // Focus on input field
    setTimeout(() => {
        document.getElementById('filePathInput').focus();
    }, 500);
}

function showFolderPathModal(targetType) {
    const modalId = 'folderPathModal';

    // Remove existing modal
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const fieldName = targetType === 'audio' ? 'Audio Output' :
                     targetType === 'source' ? 'Source Folder' : 'Destination Folder';

    const modalHTML = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-folder me-2"></i>Enter ${fieldName} Path
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Folder Selection:</strong> Enter the complete path to the ${fieldName.toLowerCase()} folder on your computer.
                        </div>
                        <div class="mb-3">
                            <label for="folderPathInput" class="form-label">
                                <strong>${fieldName} Folder Path:</strong>
                            </label>
                            <input type="text" class="form-control" id="folderPathInput"
                                   placeholder="e.g., C:\\Users\\<USER>\\Documents\\${fieldName.replace(' ', '')}"
                                   title="Enter the complete path to the ${fieldName.toLowerCase()} folder">
                            <div class="form-text">
                                Example: C:\\Users\\<USER>\\Documents\\${fieldName.replace(' ', '')}
                            </div>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> Make sure the folder exists on your computer and you have access to it.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="confirmFolderPath('${targetType}')">
                            <i class="fas fa-check me-1"></i>Confirm Path
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    // Focus on input field
    setTimeout(() => {
        document.getElementById('folderPathInput').focus();
    }, 500);
}

function confirmFilePath(targetType) {
    const filePath = document.getElementById('filePathInput').value.trim();

    if (!filePath) {
        showAlert('error', 'Please enter the complete file path');
        return;
    }

    // Update the appropriate field
    if (targetType === 'mov') {
        document.getElementById('movFilePath').value = filePath;
        showAlert('success', `Video file path set: ${filePath}`);
    }

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('filePathModal'));
    modal.hide();

    updateValidationButtonState();
}

function confirmFolderPath(targetType) {
    const folderPath = document.getElementById('folderPathInput').value.trim();

    if (!folderPath) {
        showAlert('error', 'Please enter the complete folder path');
        return;
    }

    // Update the appropriate field
    // Note: 'audio' case removed - now using fixed path E:\Extracted Audios
    if (targetType === 'source') {
        document.getElementById('sourceFolderPath').value = folderPath;
        showAlert('success', `Source folder set: ${folderPath}`);
    } else if (targetType === 'destination') {
        document.getElementById('destinationFolderPath').value = folderPath;
        showAlert('success', `Destination folder set: ${folderPath}`);
    }

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('folderPathModal'));
    modal.hide();

    updateValidationButtonState();
}

// System-wide file browser for video files (like executor functionality)
function openSystemFileBrowser(type, title) {
    console.log('📂 Opening system file browser for:', type);

    // Create file input for file selection
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.mov,.mp4,.avi,.mkv,.wmv,.flv,.webm'; // Accept various video formats
    input.style.display = 'none';

    input.addEventListener('change', function(event) {
        const files = event.target.files;
        if (files.length > 0) {
            const file = files[0];

            // Try to get the full path (works in some browsers)
            let filePath = file.name;
            if (file.path) {
                filePath = file.path; // Electron/desktop apps
            } else if (file.webkitRelativePath) {
                filePath = file.webkitRelativePath;
            }

            // If we only have the filename, show a helpful message
            if (filePath === file.name) {
                console.log('⚠️ Browser security prevents full path access. User can manually enter path.');
            }

            console.log('✅ Selected video file:', filePath);
            console.log('File details:', {
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: new Date(file.lastModified)
            });

            // Update the appropriate input field
            if (type === 'mov') {
                document.getElementById('movFilePath').value = filePath;

                if (filePath === file.name) {
                    showAlert('info', `File selected: ${file.name} (${formatFileSize(file.size)}). Please edit the field to enter the full file path.`);
                } else {
                    showAlert('success', `Video file selected: ${file.name} (${formatFileSize(file.size)})`);
                }

                // Update validation button state
                updateValidationButtonState();
            }
        }

        // Clean up
        document.body.removeChild(input);
    });

    // Add to DOM and trigger click
    document.body.appendChild(input);
    input.click();
}

// Helper function to format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// System-wide folder browser for destination folders (like executor functionality)
function openSystemFolderBrowser(type, title) {
    console.log('📁 Opening system folder browser for:', type);

    // Create file input for folder selection (webkitdirectory like executors)
    const input = document.createElement('input');
    input.type = 'file';
    input.webkitdirectory = true;
    input.multiple = true;
    input.style.display = 'none';

    input.addEventListener('change', function(event) {
        const files = event.target.files;
        if (files.length > 0) {
            // Get the folder path from the first file
            const firstFile = files[0];
            let folderPath = firstFile.webkitRelativePath.split('/')[0];
            let fullPath = folderPath;

            // Try to get the full system path
            if (firstFile.path) {
                // For Electron/desktop apps, we can get the full path
                const pathParts = firstFile.path.split('\\');
                pathParts.pop(); // Remove the filename
                fullPath = pathParts.join('\\');
            } else if (firstFile.webkitRelativePath) {
                // For web browsers, we get relative path
                const pathParts = firstFile.webkitRelativePath.split('/');
                pathParts.pop(); // Remove the filename
                fullPath = pathParts.join('/');
            }

            console.log('✅ Selected folder:', fullPath);
            console.log('Folder details:', {
                folderName: folderPath,
                fileCount: files.length,
                fullPath: fullPath
            });

            // Update the appropriate input field
            switch (type) {
                // Note: 'final' case removed - now using radio button selection
                // Note: 'audio' case removed - now using fixed path E:\Extracted Audios
                default:
                    console.log('⚠️ Unknown folder selection type:', type);
                    break;
            }

            // Update validation button state
            updateValidationButtonState();
        }

        // Clean up
        document.body.removeChild(input);
    });

    // Add to DOM and trigger click
    document.body.appendChild(input);
    input.click();
}

// Update validation button state based on required fields
function updateValidationButtonState() {
    const movFile = document.getElementById('movFilePath').value;
    const audioPath = document.getElementById('audioOutputPath').value;
    const finalPath = document.getElementById('finalFolderPath').value;

    const approveBtn = document.getElementById('approveWorkflowBtn');
    if (approveBtn) {
        const allFieldsFilled = movFile && audioPath && finalPath;
        approveBtn.disabled = !allFieldsFilled;

        if (allFieldsFilled) {
            approveBtn.innerHTML = '<i class="fas fa-check me-1"></i>Approve & Process';
            approveBtn.classList.remove('btn-secondary');
            approveBtn.classList.add('btn-success');
        } else {
            approveBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Select All Paths First';
            approveBtn.classList.remove('btn-success');
            approveBtn.classList.add('btn-secondary');
        }
    }
}

// System-wide folder browser functionality (like executors)
// No need for modal-based browser since we use native system dialogs

// Old modal-based file browser functions removed
// Now using system-wide folder selection like executors

// All old modal-based file browser functions removed
// Now using native system file/folder dialogs like executors

function setupValidationButtons() {
    const approveBtn = document.getElementById('approveWorkflowBtn');
    const rejectBtn = document.getElementById('rejectWorkflowBtn');
    const addToQueueBtn = document.getElementById('addToQueueBtn');
    const validateAllBtn = document.getElementById('validateAllFieldsBtn');

    if (approveBtn) {
        approveBtn.onclick = () => submitValidation('approve');
    }

    if (rejectBtn) {
        rejectBtn.onclick = () => submitValidation('reject');
    }

    if (addToQueueBtn) {
        addToQueueBtn.onclick = () => addToProcessingQueue();
    }

    if (validateAllBtn) {
        validateAllBtn.onclick = () => {
            const isValid = validateAllFields();
            if (isValid) {
                showAlert('success', 'All fields validated successfully!');
            } else {
                showAlert('warning', 'Please complete the required fields highlighted above.');
            }
        };
    }

    // Setup batch processing buttons
    setupBatchProcessingButtons();
}

function setupBatchProcessingButtons() {
    const startBatchBtn = document.getElementById('startBatchProcessingBtn');
    const clearQueueBtn = document.getElementById('clearQueueBtn');
    const clearLogsBtn = document.getElementById('clearLogsBtn');
    const downloadLogsBtn = document.getElementById('downloadLogsBtn');
    const cancelProcessingBtn = document.getElementById('cancelProcessingBtn');

    if (startBatchBtn) {
        startBatchBtn.onclick = () => startBatchProcessing();
    }

    if (clearQueueBtn) {
        clearQueueBtn.onclick = () => clearProcessingQueue();
    }

    if (clearLogsBtn) {
        clearLogsBtn.onclick = () => clearOperationLogs();
    }

    if (downloadLogsBtn) {
        downloadLogsBtn.onclick = () => downloadOperationLogs();
    }

    if (cancelProcessingBtn) {
        cancelProcessingBtn.onclick = () => cancelProcessing();
    }
}

function submitValidation(action) {
    console.log('🚀 Submitting validation with action:', action);

    if (!currentProcessingId) {
        showAlert('error', 'No processing ID found');
        return;
    }

    showAlert('info', 'Processing validation...');

    // Get form values
    const notes = document.getElementById('validationNotes')?.value || '';
    const editingRequired = document.getElementById('editingRequired')?.checked || false;
    const movFilePath = document.getElementById('movFilePath')?.value || '';
    const audioOutputPath = document.getElementById('audioOutputPath')?.value || '';
    let finalFolderPath = document.getElementById('finalFolderPath')?.value || '';

    // Ensure destination path is selected
    if (!finalFolderPath) {
        // Try to get from radio buttons
        const selectedRadio = document.querySelector('input[name="destinationOption"]:checked');
        if (selectedRadio) {
            finalFolderPath = selectedRadio.value;
            console.log('🎯 Retrieved destination from radio button:', finalFolderPath);
        } else {
            // Set default if nothing selected
            finalFolderPath = 'E:\\To Be Ingested';
            console.log('🎯 Using default destination:', finalFolderPath);
        }
    }

    // Validate destination path
    const validDestinations = ['E:\\To Be Deleted', 'E:\\To Be Ingested', 'E:\\For Editing'];
    if (!validDestinations.includes(finalFolderPath)) {
        showAlert('error', `Invalid destination path: ${finalFolderPath}. Must be one of: ${validDestinations.join(', ')}`);
        return;
    }

    const validationData = {
        action: action,
        notes: notes,
        editing_required: editingRequired,
        mov_file_path: movFilePath,
        audio_output_path: audioOutputPath,
        final_folder_path: finalFolderPath,
        updated_metadata: currentMetadata
    };

    console.log('📤 Submitting validation data:', validationData);

    fetch(`/api/validate_folder/${currentProcessingId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validationData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Validation completed successfully! Metadata uploaded to Google Sheets, audio extracted, and folder moved.');
            const modal = bootstrap.Modal.getInstance(document.getElementById('validationWorkflowModal'));
            if (modal) modal.hide();
            setTimeout(() => window.location.reload(), 2000);
        } else {
            showAlert('error', 'Validation failed: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('❌ Validation error:', error);
        showAlert('error', 'Network error during validation');
    });
}

// Batch Processing Functions
function addToProcessingQueue() {
    if (!currentProcessingId || !currentFolderName || !currentMetadata) {
        showAlert('error', 'No folder data available to add to queue');
        return;
    }

    // Validate required fields
    const movFile = document.getElementById('movFilePath').value;
    const audioPath = document.getElementById('audioOutputPath').value;
    const sourcePath = document.getElementById('sourceFolderPath').value;
    let destinationPath = document.getElementById('finalFolderPath').value;

    // If destination path is empty, try to get from radio button selection
    if (!destinationPath) {
        const selectedRadio = document.querySelector('input[name="destinationOption"]:checked');
        if (selectedRadio) {
            destinationPath = selectedRadio.value;
            console.log('🎯 Retrieved destination from radio button for queue:', destinationPath);
        } else {
            // Set default if nothing selected
            destinationPath = 'E:\\To Be Ingested';
            console.log('🎯 Using default destination for queue:', destinationPath);
        }
    }

    if (!movFile || !audioPath || !sourcePath || !destinationPath) {
        showAlert('error', 'Please select all required paths: video file, audio destination, source folder, and destination folder');
        console.error('❌ Missing required fields:', {
            movFile: !!movFile,
            audioPath: !!audioPath,
            sourcePath: !!sourcePath,
            destinationPath: !!destinationPath
        });
        return;
    }

    console.log('✅ All required fields validated for processing queue');

    // Create queue item
    const queueItem = {
        id: currentProcessingId,
        folderName: currentFolderName,
        metadata: currentMetadata,
        movFilePath: movFile,
        audioOutputPath: audioPath,
        sourceFolderPath: sourcePath,
        final_folder_path: destinationPath, // Use the backend field name for consistency
        destinationFolderPath: destinationPath, // Keep for backward compatibility
        editingRequired: document.getElementById('editingRequired')?.checked || false,
        notes: document.getElementById('validationNotes')?.value || '',
        addedAt: new Date().toISOString()
    };

    console.log('📋 Adding to processing queue:', queueItem);

    // Add to queue
    processingQueue.push(queueItem);

    // Update UI
    updateProcessingQueueDisplay();
    updateQueueCounter();

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('validationWorkflowModal'));
    if (modal) modal.hide();

    logOperation('success', `Added "${currentFolderName}" to processing queue`);
    showAlert('success', `Folder "${currentFolderName}" added to processing queue`);
}

function updateProcessingQueueDisplay() {
    const queueContainer = document.getElementById('processingQueue');
    if (!queueContainer) return;

    if (processingQueue.length === 0) {
        queueContainer.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No items in processing queue</h5>
                <p class="text-muted">Add folders to the queue using the "Add to Processing Queue" button in the validation modal.</p>
            </div>
        `;
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr>';
    html += '<th>Folder Name</th><th>Video File</th><th>Audio Destination</th><th>Final Destination</th><th>Status</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    processingQueue.forEach((item, index) => {
        const statusBadge = item.status === 'completed' ? 'bg-success' :
                           item.status === 'processing' ? 'bg-warning' :
                           item.status === 'error' ? 'bg-danger' : 'bg-secondary';

        html += `
            <tr ${item.status === 'processing' ? 'class="table-warning"' : ''}>
                <td><strong>${item.folderName}</strong></td>
                <td><small>${item.movFilePath.split('/').pop() || item.movFilePath.split('\\').pop()}</small></td>
                <td><small>${item.audioOutputPath}</small></td>
                <td><small>${item.finalFolderPath}</small></td>
                <td><span class="badge ${statusBadge}">${item.status || 'Queued'}</span></td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="removeFromQueue(${index})"
                            ${item.status === 'processing' ? 'disabled' : ''}>
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    queueContainer.innerHTML = html;
}

function updateQueueCounter() {
    const queueCount = document.getElementById('queueCount');
    const startBtn = document.getElementById('startBatchProcessingBtn');

    if (queueCount) {
        queueCount.textContent = processingQueue.length;
    }

    if (startBtn) {
        startBtn.disabled = processingQueue.length === 0 || isProcessing;
    }
}

function removeFromQueue(index) {
    const item = processingQueue[index];
    processingQueue.splice(index, 1);

    updateProcessingQueueDisplay();
    updateQueueCounter();

    logOperation('info', `Removed "${item.folderName}" from processing queue`);
    showAlert('info', `Removed "${item.folderName}" from queue`);
}

function clearProcessingQueue() {
    if (isProcessing) {
        showAlert('error', 'Cannot clear queue while processing is in progress');
        return;
    }

    if (processingQueue.length === 0) {
        showAlert('info', 'Queue is already empty');
        return;
    }

    if (confirm(`Are you sure you want to clear all ${processingQueue.length} items from the queue?`)) {
        processingQueue = [];
        updateProcessingQueueDisplay();
        updateQueueCounter();

        logOperation('warning', 'Processing queue cleared');
        showAlert('warning', 'Processing queue cleared');
    }
}

// Live Operation Logging Functions
function logOperation(type, message) {
    const logsContainer = document.getElementById('operationLogs');
    if (!logsContainer) return;

    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'success' ? 'check-circle text-success' :
                 type === 'error' ? 'exclamation-triangle text-danger' :
                 type === 'warning' ? 'exclamation-circle text-warning' :
                 'info-circle text-info';

    const logEntry = document.createElement('div');
    logEntry.className = 'mb-2';
    logEntry.innerHTML = `
        <span class="text-muted">[${timestamp}]</span>
        <i class="fas fa-${icon} me-2"></i>
        <span>${message}</span>
    `;

    logsContainer.appendChild(logEntry);
    logsContainer.scrollTop = logsContainer.scrollHeight;
}

function clearOperationLogs() {
    const logsContainer = document.getElementById('operationLogs');
    if (logsContainer) {
        logsContainer.innerHTML = `
            <div class="text-muted">
                <i class="fas fa-info-circle me-2"></i>Operation logs cleared - new operations will appear here...
            </div>
        `;
    }
}

function downloadOperationLogs() {
    const logsContainer = document.getElementById('operationLogs');
    if (!logsContainer) return;

    const logs = logsContainer.innerText;
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `crosschecker-logs-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    logOperation('info', 'Operation logs downloaded');
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Batch Processing Execution Functions
function startBatchProcessing() {
    if (processingQueue.length === 0) {
        showAlert('error', 'No items in processing queue');
        return;
    }

    if (isProcessing) {
        showAlert('error', 'Processing is already in progress');
        return;
    }

    isProcessing = true;
    currentProcessingIndex = 0;

    // Show progress modal
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();

    // Update UI
    updateQueueCounter();

    // Start processing
    logOperation('info', `Starting batch processing of ${processingQueue.length} items`);
    processNextItem();
}

function processNextItem() {
    if (currentProcessingIndex >= processingQueue.length) {
        // All items processed
        completeBatchProcessing();
        return;
    }

    const item = processingQueue[currentProcessingIndex];
    item.status = 'processing';

    // Update displays
    updateProcessingQueueDisplay();
    updateProgressDisplay();

    logOperation('info', `Processing item ${currentProcessingIndex + 1}/${processingQueue.length}: ${item.folderName}`);

    // Process the item
    processAudioItem(item)
        .then(() => {
            item.status = 'completed';
            logOperation('success', `Completed processing: ${item.folderName}`);
            currentProcessingIndex++;
            setTimeout(() => processNextItem(), 1000); // Small delay between items
        })
        .catch(error => {
            item.status = 'error';
            item.error = error.message;
            logOperation('error', `Failed processing ${item.folderName}: ${error.message}`);
            currentProcessingIndex++;
            setTimeout(() => processNextItem(), 1000); // Continue with next item
        });
}

async function processAudioItem(item) {
    try {
        // Update current operation
        updateCurrentOperation(`Processing: ${item.folderName}`);

        // Generate audio filename using the specified format
        const audioCode = item.metadata.audio_code || 'UNKNOWN';
        const softRenamed = item.metadata.soft_renamed || item.folderName;
        const audioFileName = `${audioCode}_${softRenamed}.wav`;

        // Step 1: Audio Extraction
        updateStepStatus('step1', 'processing');
        updateStepProgress(25, 'Extracting audio from video file...');

        logOperation('info', `Starting audio extraction: ${item.movFilePath} → ${audioFileName}`);

        // Make API call for audio processing
        const response = await fetch('/api/process_audio_batch', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ items: [item] })
        });

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || 'Audio processing failed');
        }

        const result = data.results[0];
        if (!result.success) {
            throw new Error(result.error || 'Audio processing failed');
        }

        updateStepStatus('step1', 'completed');
        logOperation('success', `Audio extracted successfully: ${audioFileName}`);

        // Step 2: File Renaming
        updateStepStatus('step2', 'processing');
        updateStepProgress(50, 'Audio file renamed with format: AudioCode_SoftRenamedName.wav');

        logOperation('info', `Audio file saved as: ${result.audioFilename}`);
        await simulateAsyncOperation(500); // Small delay for UI
        updateStepStatus('step2', 'completed');
        logOperation('success', `Audio file renamed successfully: ${result.audioFilename}`);

        // Step 3: Folder Movement & Renaming
        updateStepStatus('step3', 'processing');
        updateStepProgress(75, 'Moving and renaming folder...');

        logOperation('info', `Folder moved to: ${result.finalFolderPath}`);
        await simulateAsyncOperation(500); // Small delay for UI
        updateStepStatus('step3', 'completed');
        logOperation('success', 'Folder moved and renamed successfully');

        // Step 4: Google Sheets Update
        updateStepStatus('step4', 'processing');
        updateStepProgress(100, 'Updating Google Sheets...');

        logOperation('info', 'Updating Google Sheets with metadata...');
        await simulateAsyncOperation(500); // Small delay for UI
        updateStepStatus('step4', 'completed');
        logOperation('success', 'Data updated in Google Sheet (Main Sheet)');

        // Reset step statuses for next item
        setTimeout(() => resetStepStatuses(), 500);

        return result;

    } catch (error) {
        logOperation('error', `Processing failed: ${error.message}`);
        throw error;
    }
}

function updateProgressDisplay() {
    const overallProgress = ((currentProcessingIndex / processingQueue.length) * 100);
    const progressBar = document.getElementById('overallProgress');
    const currentItem = document.getElementById('currentItem');
    const totalItems = document.getElementById('totalItems');

    if (progressBar) {
        progressBar.style.width = `${overallProgress}%`;
        progressBar.textContent = `${Math.round(overallProgress)}%`;
        progressBar.setAttribute('aria-valuenow', overallProgress);
    }

    if (currentItem) currentItem.textContent = currentProcessingIndex + 1;
    if (totalItems) totalItems.textContent = processingQueue.length;
}

function updateCurrentOperation(message) {
    const currentOp = document.getElementById('currentOperation');
    if (currentOp) {
        currentOp.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${message}`;
    }
}

function updateStepProgress(percentage, description) {
    const stepProgress = document.getElementById('stepProgress');
    const stepDesc = document.getElementById('stepDescription');

    if (stepProgress) {
        stepProgress.style.width = `${percentage}%`;
        stepProgress.textContent = `${percentage}%`;
        stepProgress.setAttribute('aria-valuenow', percentage);
    }

    if (stepDesc) {
        stepDesc.textContent = description;
    }
}

function updateStepStatus(stepId, status) {
    const step = document.getElementById(stepId);
    if (!step) return;

    const badge = step.querySelector('.badge');
    if (!badge) return;

    switch (status) {
        case 'processing':
            badge.className = 'badge bg-warning';
            badge.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing';
            break;
        case 'completed':
            badge.className = 'badge bg-success';
            badge.innerHTML = '<i class="fas fa-check me-1"></i>Completed';
            break;
        case 'error':
            badge.className = 'badge bg-danger';
            badge.innerHTML = '<i class="fas fa-times me-1"></i>Error';
            break;
        default:
            badge.className = 'badge bg-secondary';
            badge.textContent = 'Pending';
    }
}

function resetStepStatuses() {
    ['step1', 'step2', 'step3', 'step4'].forEach(stepId => {
        updateStepStatus(stepId, 'pending');
    });
    updateStepProgress(0, 'Ready for next item...');
}

function completeBatchProcessing() {
    isProcessing = false;

    // Update UI
    updateQueueCounter();
    updateProcessingQueueDisplay();

    // Close progress modal
    const progressModal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
    if (progressModal) progressModal.hide();

    // Show completion message
    const completedCount = processingQueue.filter(item => item.status === 'completed').length;
    const errorCount = processingQueue.filter(item => item.status === 'error').length;

    logOperation('success', `Batch processing completed! ${completedCount} successful, ${errorCount} errors`);
    showAlert('success', `Batch processing completed! ${completedCount} items processed successfully.`);

    if (errorCount > 0) {
        showAlert('warning', `${errorCount} items had errors. Check the logs for details.`);
    }
}

function cancelProcessing() {
    if (!isProcessing) return;

    if (confirm('Are you sure you want to cancel the current processing?')) {
        isProcessing = false;

        // Update current item status
        if (currentProcessingIndex < processingQueue.length) {
            processingQueue[currentProcessingIndex].status = 'cancelled';
        }

        // Update UI
        updateQueueCounter();
        updateProcessingQueueDisplay();

        // Close progress modal
        const progressModal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
        if (progressModal) progressModal.hide();

        logOperation('warning', 'Processing cancelled by user');
        showAlert('warning', 'Processing cancelled');
    }
}

// Utility function to simulate async operations
function simulateAsyncOperation(duration) {
    return new Promise(resolve => setTimeout(resolve, duration));
}

// Server-side file browser functions
function openServerFileBrowser(browseType, targetType) {
    console.log(`🔍 Opening server file browser: ${browseType} for ${targetType}`);
    showFileBrowserModal(browseType, targetType);
}

function showFileBrowserModal(browseType, targetType) {
    const modalId = 'serverFileBrowserModal';

    // Remove existing modal if any
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const title = browseType === 'file' ? 'Select Video File' : 'Select Folder';

    const modalHTML = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-${browseType === 'file' ? 'file-video' : 'folder'} me-2"></i>
                            ${title}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-folder"></i>
                                </span>
                                <input type="text" class="form-control" id="currentBrowserPath" value="Computer" readonly>
                                <button class="btn btn-outline-secondary" onclick="navigateUp()" title="Go up">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                                <button class="btn btn-outline-primary" onclick="refreshBrowser()" title="Refresh">
                                    <i class="fas fa-sync"></i>
                                </button>
                            </div>
                        </div>
                        <div id="fileBrowserContent" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 10px;">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="mt-2">Loading directory...</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                ${browseType === 'file' ?
                                  'Click on a video file (.mov, .mp4, .avi) to select it.' :
                                  'Navigate to the desired folder and click "Select Current Folder".'}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        ${browseType === 'folder' ?
                          `<button type="button" class="btn btn-primary" onclick="selectCurrentFolder()">
                             <i class="fas fa-check me-1"></i>Select Current Folder
                           </button>` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Store current browse context
    window.currentBrowseContext = { browseType, targetType };

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    // Load initial directory (start at E:\Work space for validation workflow)
    loadBrowserDirectory('E:\\Work space');
}

async function loadBrowserDirectory(path) {
    try {
        // Ensure we always start from E:\Work space for validation workflow
        if (!path || path === '/' || path === '' || path === 'drives') {
            path = 'E:\\Work space';
        }

        // Security: Ensure path stays within E:\Work space for validation workflow
        if (!path.toUpperCase().startsWith('E:\\WORK SPACE')) {
            console.warn('🔒 Path outside E:\\Work space detected, redirecting to workspace:', path);
            path = 'E:\\Work space';
        }

        const response = await fetch('/api/browse_files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                path: path,
                type: window.currentBrowseContext.browseType === 'file' ? 'video' : 'all',
                context: 'validation_workflow'  // Indicate this is for validation workflow
            })
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('currentBrowserPath').value = data.path;
            displayBrowserItems(data.items);
        } else {
            showBrowserError(`Error: ${data.error}`);
        }
    } catch (error) {
        showBrowserError(`Network error: ${error.message}`);
    }
}

function displayBrowserItems(items) {
    const container = document.getElementById('fileBrowserContent');

    if (items.length === 0) {
        container.innerHTML = '<div class="text-muted text-center p-3">No items found</div>';
        return;
    }

    let html = '<div class="list-group list-group-flush">';

    items.forEach(item => {
        const isFolder = item.type === 'folder';
        const isDrive = item.type === 'drive';
        const isVideo = !isFolder && !isDrive && item.name.toLowerCase().match(/\.(mov|mp4|avi|mkv|wmv)$/);

        let icon, textClass, clickAction;

        if (isDrive) {
            icon = 'fas fa-hdd text-primary';
            textClass = 'text-dark fw-bold';
            clickAction = `loadBrowserDirectory('${item.path.replace(/\\/g, '\\\\')}')`;
        } else if (isFolder) {
            icon = 'fas fa-folder text-warning';
            textClass = 'text-dark';
            clickAction = `loadBrowserDirectory('${item.path.replace(/\\/g, '\\\\')}')`;
        } else if (isVideo) {
            icon = 'fas fa-file-video text-info';
            textClass = 'text-dark';
            clickAction = window.currentBrowseContext.browseType === 'file' ?
                         `selectBrowserFile('${item.path.replace(/\\/g, '\\\\')}')` : '';
        } else {
            icon = 'fas fa-file text-muted';
            textClass = 'text-muted';
            clickAction = '';
        }

        const clickable = clickAction ? 'list-group-item-action' : '';
        const cursor = clickAction ? 'cursor-pointer' : '';

        html += `
            <div class="list-group-item ${clickable} d-flex justify-content-between align-items-center ${cursor}"
                 ${clickAction ? `onclick="${clickAction}"` : ''}>
                <div class="${textClass}">
                    <i class="${icon} me-2"></i>
                    ${item.name}
                </div>
                <small class="text-muted">
                    ${isDrive ? 'Drive' : isFolder ? 'Folder' : formatFileSize(item.size)}
                </small>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

function selectBrowserFile(filePath) {
    // Update the video file input field
    document.getElementById('movFilePath').value = filePath;
    showAlert('success', `Video file selected: ${filePath}`);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('serverFileBrowserModal'));
    modal.hide();

    updateValidationButtonState();
}

function selectCurrentFolder() {
    const currentPath = document.getElementById('currentBrowserPath').value;
    const { targetType } = window.currentBrowseContext;

    // Update the appropriate input field
    // Note: 'audio' case removed - now using fixed path E:\Extracted Audios
    if (targetType === 'source') {
        document.getElementById('sourceFolderPath').value = currentPath;
        showAlert('success', `Source folder selected: ${currentPath}`);
    } else if (targetType === 'destination') {
        document.getElementById('destinationFolderPath').value = currentPath;
        showAlert('success', `Destination folder selected: ${currentPath}`);
    }

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('serverFileBrowserModal'));
    modal.hide();

    updateValidationButtonState();
}

function navigateUp() {
    const currentPath = document.getElementById('currentBrowserPath').value;

    if (currentPath === 'Computer') {
        // Already at the top level
        return;
    }

    // If we're at a drive root (like C:\), go back to drives list
    if (currentPath.match(/^[A-Z]:\\?$/)) {
        loadBrowserDirectory('drives');
        return;
    }

    // Otherwise, go up one directory level
    const parentPath = currentPath.split('\\').slice(0, -1).join('\\');
    if (parentPath && parentPath !== currentPath) {
        loadBrowserDirectory(parentPath);
    }
}

function refreshBrowser() {
    const currentPath = document.getElementById('currentBrowserPath').value;
    loadBrowserDirectory(currentPath);
}

function showBrowserError(message) {
    const container = document.getElementById('fileBrowserContent');
    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

// Editable Metadata Field Functions
function editField(fieldName) {
    console.log('🖊️ Editing field:', fieldName);

    const displayElement = document.getElementById(`display_${fieldName}`);
    const inputElement = document.getElementById(`edit_${fieldName}`);

    if (displayElement && inputElement) {
        // Hide display, show input
        displayElement.style.display = 'none';
        inputElement.style.display = 'block';

        // Focus on input
        inputElement.focus();

        // Select text if it's a text input
        if (inputElement.type === 'text' || inputElement.type === 'url') {
            inputElement.select();
        }

        // Update auto-save status
        updateAutoSaveStatus('Editing...');
    }
}

function saveField(fieldName) {
    console.log('💾 Saving field:', fieldName);

    const displayElement = document.getElementById(`display_${fieldName}`);
    const inputElement = document.getElementById(`edit_${fieldName}`);

    if (displayElement && inputElement) {
        let newValue = '';

        // Get value based on input type
        if (inputElement.type === 'checkbox') {
            newValue = inputElement.checked;
            // Update the label for toggle
            const label = inputElement.nextElementSibling;
            if (label) {
                label.textContent = newValue ? 'Yes' : 'No';
            }
        } else {
            newValue = inputElement.value.trim();
        }

        // Update current metadata
        if (!window.currentEditableMetadata) {
            window.currentEditableMetadata = {};
        }

        // Handle special fields
        if (fieldName === 'content_tags') {
            // Convert comma-separated string to array
            window.currentEditableMetadata[fieldName] = newValue ? newValue.split(',').map(tag => tag.trim()) : [];
        } else {
            window.currentEditableMetadata[fieldName] = newValue;
        }

        // Update display
        if (inputElement.type !== 'checkbox') {
            const displayValue = newValue || 'Click to add...';
            displayElement.textContent = displayValue;
            displayElement.className = `form-control-plaintext border rounded p-2 bg-light editable-display ${newValue ? '' : 'text-muted'}`;

            // Hide input, show display
            inputElement.style.display = 'none';
            displayElement.style.display = 'block';
        }

        // Handle social media toggle
        if (fieldName === 'is_social_media') {
            const socialMediaSection = document.getElementById('socialMediaSection');
            if (socialMediaSection) {
                socialMediaSection.style.display = newValue ? 'block' : 'none';
            }
        }

        // Auto-save
        autoSaveMetadata();

        // Update validation status
        validateAllFields();

        console.log('✅ Field saved:', fieldName, '=', newValue);
    }
}

function handleEnterKey(event, fieldName) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        saveField(fieldName);
    }
}

function autoSaveMetadata() {
    updateAutoSaveStatus('Saving...');

    // Simulate auto-save delay
    setTimeout(() => {
        // Store in session storage
        if (window.currentEditableMetadata) {
            sessionStorage.setItem('crosschecker_metadata', JSON.stringify(window.currentEditableMetadata));
        }

        updateAutoSaveStatus('Saved');

        // Reset status after 2 seconds
        setTimeout(() => {
            updateAutoSaveStatus('Ready');
        }, 2000);
    }, 500);
}

function updateAutoSaveStatus(status) {
    const statusElement = document.getElementById('autoSaveStatus');
    if (statusElement) {
        statusElement.textContent = status;

        // Add visual feedback
        statusElement.className = status === 'Saved' ? 'text-success fw-bold' :
                                 status === 'Saving...' ? 'text-warning' :
                                 'text-muted';
    }
}

function validateAllFields() {
    console.log('🔍 Validating all fields...');

    const validationStatus = document.getElementById('validationStatus');
    if (!validationStatus || !window.currentEditableMetadata) return;

    const metadata = window.currentEditableMetadata;
    const requiredFields = ['audio_code', 'soft_renamed', 'ocd_number', 'title'];
    const missingFields = [];
    const warnings = [];

    // Check required fields
    requiredFields.forEach(field => {
        if (!metadata[field] || metadata[field].toString().trim() === '') {
            missingFields.push(field.replace('_', ' ').toUpperCase());
        }
    });

    // Check social media fields if applicable
    if (metadata.is_social_media) {
        const socialMediaFields = ['video_id', 'post_date', 'platform_name'];
        socialMediaFields.forEach(field => {
            if (!metadata[field] || metadata[field].toString().trim() === '') {
                warnings.push(`Social Media: ${field.replace('_', ' ').toUpperCase()}`);
            }
        });
    }

    // Update validation status
    let statusHTML = '';
    let statusClass = 'alert-success';

    if (missingFields.length > 0) {
        statusClass = 'alert-danger';
        statusHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Missing Required Fields:</strong> ${missingFields.join(', ')}
        `;
    } else if (warnings.length > 0) {
        statusClass = 'alert-warning';
        statusHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong>Recommended Fields:</strong> ${warnings.join(', ')}
        `;
    } else {
        statusHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <strong>All fields validated successfully!</strong> Ready for processing.
        `;
    }

    validationStatus.className = `alert ${statusClass}`;
    validationStatus.innerHTML = statusHTML;

    return missingFields.length === 0;
}

// Duplicate function removed - using the main setupValidationButtons() function above

// Audio Code Release Function
async function releaseAudioCode(audioCode) {
    console.log('🔓 Releasing audio code:', audioCode);

    if (!audioCode || audioCode.trim() === '') {
        showAlert('error', 'No audio code to release');
        return;
    }

    // Confirm action
    if (!confirm(`Are you sure you want to release audio code "${audioCode}"?\n\nThis will mark it as "Code to be reused" and make it available for future use.`)) {
        return;
    }

    const button = document.getElementById('releaseCodeBtn');

    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Releasing...';
    button.disabled = true;

    try {
        const response = await fetch('/api/release_audio_code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                audio_code: audioCode.trim()
            })
        });

        const result = await response.json();

        if (result.success) {
            // Show success message
            showAlert('success', `Audio code ${result.audio_code} has been released and marked as "Code to be reused"`);

            console.log('✅ Audio code released:', result.audio_code);

            // Update button state to show it's been released
            button.innerHTML = '<i class="fas fa-check me-1"></i>Released';
            button.classList.remove('btn-outline-warning');
            button.classList.add('btn-success');

            // Keep button disabled since code is now released
            button.disabled = true;

        } else {
            // Show error message
            showAlert('error', result.error || 'Failed to release audio code');
            console.error('❌ Release failed:', result.error);

            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        }

    } catch (error) {
        console.error('❌ Network error:', error);
        showAlert('error', 'Network error occurred while releasing audio code');

        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Google Sheets Search Functionality for Cross-Checker
function searchGoogleSheetsInCrossChecker(event) {
    const videoIdField = document.getElementById('edit_video_id');
    const videoId = videoIdField ? videoIdField.value.trim() : '';

    if (!videoId) {
        showAlert('warning', 'Please enter a Video ID first.');
        return;
    }

    // Show loading state
    const button = event ? event.target : document.getElementById('searchGoogleSheetsBtn');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Searching...';
    button.disabled = true;

    console.log('🔍 Searching Google Sheets for Video ID:', videoId);

    fetch('/api/search_google_sheets_crosschecker', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ video_id: videoId })
    })
    .then(response => response.json())
    .then(data => {
        console.log('📊 Google Sheets search response:', data);

        if (data.success && data.data) {
            // Auto-fill fields with found data
            const fieldsToUpdate = {
                'title': data.data.title,
                'description': data.data.description,
                'duration': data.data.duration,
                'platform': data.data.platform,
                'transcription_status': 'Applicable', // Always set to Applicable
                'url': data.data.url,
                'project_date': data.data.date, // Map date to project_date
                'transcription_file': data.data.transcription_file
            };

            let updatedFields = [];

            // Update each field if data is available
            Object.entries(fieldsToUpdate).forEach(([fieldName, value]) => {
                if (value) {
                    // Update the display value
                    const displayElement = document.getElementById(`display_${fieldName}`);
                    const inputElement = document.getElementById(`edit_${fieldName}`);

                    if (displayElement && inputElement) {
                        displayElement.textContent = value;
                        displayElement.classList.remove('text-muted');
                        inputElement.value = value;

                        // Update the metadata in memory
                        if (window.currentMetadata) {
                            window.currentMetadata[fieldName] = value;
                        }

                        updatedFields.push(fieldName);
                    }
                }
            });

            if (updatedFields.length > 0) {
                showAlert('success', `Data found and populated! Updated fields: ${updatedFields.join(', ')}`);
                console.log('✅ Updated fields:', updatedFields);
            } else {
                showAlert('info', 'Video ID found but no additional data available to populate.');
            }
        } else {
            showAlert('warning', data.error || 'Video ID not found in Google Sheets.');
        }
    })
    .catch(error => {
        console.error('❌ Error searching Google Sheets:', error);
        showAlert('error', 'Network error while searching Google Sheets.');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}
